﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Spire.Doc;
using Spire.Doc.Documents;
using Spire.Doc.Fields;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Api;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Utils;
using Document = Spire.Doc.Document;

namespace UNI.Master.BLL.BusinessService.Common
{
    public class WordService : IWordService
    {
        private readonly ILogger<IWordService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IHostingEnvironment _hostingEnvironment;
        private readonly IApiStorageService _storageService;
        //private readonly IContractTypeService _contractTypeSrv;
        public WordService(
            IConfiguration configuration,
            IHostingEnvironment hostingEnvironment,
            IApiStorageService storageService,
            ILogger<IWordService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _hostingEnvironment = hostingEnvironment;
            _storageService = storageService;
            //_contractTypeSrv = contractTypeSrv;
        }

        public async Task<MetaTempResponse<ContractMeta>> CreateBill_MetaFiles(MetaTempResponse<ContractMeta> input)
        {
            var sohopdong = "";

            input.data.TryGetValue("id", out sohopdong);

            try
            {
                foreach (var mt in input.metafiles)
                {
                    Stream outStream = null;
                    //Stream stream_meta_file_id_0b = null;

                    var pfile = $"contracts/{sohopdong}-{mt.tempId}/{mt.meta_title}-{Guid.NewGuid().ToString()}.pdf";
                    var file_backend_docx = $"contracts-backend/{sohopdong}-{mt.tempId}/{mt.meta_title}-{Guid.NewGuid().ToString()}.docx";
                    var file_backend_xlsx = $"contracts-backend/{sohopdong}-{mt.tempId}/{mt.meta_title}-{Guid.NewGuid().ToString()}.xlsx";

                    if (mt.meta_file_tpl.Contains("xlsx"))
                    {
                        var template = AppContext.BaseDirectory + "wwwroot" + mt.meta_file_tpl;
                        DataTable table = new DataTable();

                        FlexcellUtils r = new FlexcellUtils();
                        outStream = r.CreateReportPDF(File.ReadAllBytes(template), ReportType.pdf, table, input.data);
                        //stream_meta_file_id_0b = r.CreateReportPDF(File.ReadAllBytes(template), ReportType.xlsx, table, input.data);

                        if (outStream != null)
                        {
                            var uploadResponse = await _storageService.UploadFile(outStream, pfile, mt.meta_title);
                            mt.temp_download_url = uploadResponse.Url;
                            mt.meta_file_url = uploadResponse.Url;
                        }

                    }
                    if (mt.meta_file_tpl.Contains("docx") || mt.meta_file_tpl.Contains("doc"))
                    {
                        string template = AppContext.BaseDirectory + "wwwroot" + mt.meta_file_tpl; 

                        MemoryStream stream = new MemoryStream();
                        var fileContent = new Document(template);
                        var data = input.data;
                        if (data.Count > 0)
                        {
                            try
                            {
                                foreach (var item in data)
                                {
                                    var str_value = "";
                                    if (item.Value != null) str_value = item.Value;
                                    fileContent.Replace("{{" + item.Key + "}}", str_value, true, true);
                                }
                                fileContent.SaveToStream(stream, FileFormat.Docx);
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine(ex.Message);
                            }
                        }

                        try
                        {
                            {
                                outStream = OfficeUtil.WordToPdf(stream);
                                if (outStream != null)
                                {
                                    var uploadResponse = await _storageService.UploadFile(outStream, pfile, mt.meta_title);
                                    mt.temp_download_url = uploadResponse.Url;
                                    mt.meta_file_url = uploadResponse.Url;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("loi khong tao duoc file pdf: " + ex.Message);
                            //outStream = wordToPdf.ConvertDocxToPdf(stream);
                        }
                    }


                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
            return input;
            //return Task.FromResult(new hrmEmpMetaResponse());
        }

        //public async Task<MetaTempResponse<ContractMeta>> CreateHRM_MetaFiles(MetaTempResponse<ContractMeta> input)
        //{
        //    var sohopdong = "";
        //    //Google.Apis.Storage.v1.Data.Object pathfile = null;

        //    input.data.TryGetValue("contract_no", out sohopdong);

        //    //try
        //    //{
        //    foreach (var mt in input.metafiles)
        //    {
        //        Stream outStream = null;
        //        Stream stream_meta_file_id_0b = null;
        //        var template = AppContext.BaseDirectory + "wwwroot" + mt.meta_file_tpl;//Path.Combine(AppContext.BaseDirectory, "wwwroot", mt.meta_file_tpl);//

        //        var pfile = $"contracts/{sohopdong}-{mt.tempId}/{mt.meta_title}-{Guid.NewGuid().ToString()}.pdf";
        //        var file_backend_docx = $"contracts-backend/{sohopdong}-{mt.tempId}/{mt.meta_title}-{Guid.NewGuid().ToString()}.docx";
        //        var file_backend_xlsx = $"contracts-backend/{sohopdong}-{mt.tempId}/{mt.meta_title}-{Guid.NewGuid().ToString()}.xlsx";

        //        byte[] fileBytes = null;
        //        if (mt.tempId != null && mt.tempId != Guid.Empty)
        //        {
        //            var resGetTempFile = await _contractTypeSrv.GetContractTemplateFile(mt.tempId.Value, input.CompanyId);
        //            if (resGetTempFile.valid && resGetTempFile.Data != null && resGetTempFile.Data.Length > 0)
        //                fileBytes = resGetTempFile.Data;
        //        }

        //        if ((fileBytes == null || fileBytes.Length <= 0) && File.Exists(template))
        //            fileBytes = File.ReadAllBytes(template);

        //        if (fileBytes != null && fileBytes.Length > 0 && File.Exists(template))//if (File.Exists(template))
        //        {
        //            if (mt.meta_file_tpl.Contains("xlsx"))
        //            {

        //                DataTable table = new DataTable();

        //                FlexcellUtils r = new FlexcellUtils();
        //                outStream = r.CreateReportPDF(fileBytes, ReportType.pdf, table, input.data);
        //                stream_meta_file_id_0b = r.CreateReportPDF(fileBytes, ReportType.xlsx, table, input.data);

        //                // lưu trữ file hợp đồng cho backend
        //                if (stream_meta_file_id_0b != null)
        //                {
        //                    // pathfile = await FireBaseServices.UploadFile(stream_meta_file_id_0b, $"{file_backend_xlsx}", app: "s_hrm", format: "application/xlsx");
        //                    // mt.meta_file_id_0b = pathfile.MediaLink.Replace("https://storage.googleapis.com/download/storage/v1/b/sunshine-app-production.appspot.com/o/", "https://cdn.sunshineapp.vn/");

        //                    var uploadResponse = await _storageService.UploadFile(stream_meta_file_id_0b, file_backend_xlsx, mt.meta_title, "application/xlsx");
        //                    mt.meta_file_id_0b = uploadResponse.Url;
        //                }

        //                if (outStream != null)
        //                {
        //                    // pathfile = await FireBaseServices.UploadFile(outStream, $"{pfile}", app: "s_hrm");
        //                    // mt.temp_download_url = pathfile.MediaLink.Replace("https://storage.googleapis.com/download/storage/v1/b/sunshine-app-production.appspot.com/o/", "https://cdn.sunshineapp.vn/");
        //                    // mt.meta_file_url = mt.temp_download_url;

        //                    var uploadResponse = await _storageService.UploadFile(outStream, pfile, mt.meta_title);
        //                    mt.temp_download_url = uploadResponse.Url;
        //                    mt.meta_file_url = uploadResponse.Url;
        //                }

        //            }
        //            if (mt.meta_file_tpl.Contains("docx") || mt.meta_file_tpl.Contains("doc"))
        //            {
        //                //string template = AppContext.BaseDirectory + "wwwroot" + mt.meta_file_tpl; // Path.Combine(AppContext.BaseDirectory, "wwwroot", mt.meta_file_tpl);                                                                          
        //                //byte[] streambyte = MiniWordUtils.SaveAsByTemplateStream(template, input.data, null);
        //                //MemoryStream stream = new MemoryStream(streambyte);

        //                MemoryStream stream = new MemoryStream();
        //                using var ms = new MemoryStream(fileBytes);
        //                var fileContent = new Document(ms);
        //                var data = input.data;
        //                if (data.Count > 0)
        //                {
        //                    try
        //                    {
        //                        foreach (var item in data)
        //                        {
        //                            var str_value = "";
        //                            if (item.Value != null) str_value = item.Value;
        //                            fileContent.Replace("{{" + item.Key + "}}", str_value, true, true);
        //                        }
        //                        fileContent.SaveToStream(stream, FileFormat.Docx);
        //                    }
        //                    catch (Exception ex)
        //                    {
        //                        Console.WriteLine(ex.Message);
        //                    }
        //                }
        //                // lưu trữ file hợp đồng cho backend
        //                if (stream != null)
        //                {
        //                    // pathfile = await FireBaseServices.UploadFile(stream, $"{file_backend_docx}", app: "s_hrm", format: "application/docx");
        //                    // mt.meta_file_id_0b = pathfile.MediaLink.Replace("https://storage.googleapis.com/download/storage/v1/b/sunshine-app-production.appspot.com/o/", "https://cdn.sunshineapp.vn/");

        //                    var uploadResponse = await _storageService.UploadFile(stream, file_backend_docx, mt.meta_title, "application/docx");
        //                    mt.meta_file_id_0b = uploadResponse.Url;
        //                }

        //                //try
        //                //{
        //                //{
        //                outStream = OfficeUtil.WordToPdf(stream);
        //                if (outStream != null)
        //                {
        //                    // pathfile = await FireBaseServices.UploadFile(outStream, $"{pfile}", app: "s_hrm");
        //                    // mt.temp_download_url = pathfile.MediaLink.Replace("https://storage.googleapis.com/download/storage/v1/b/sunshine-app-production.appspot.com/o/", "https://cdn.sunshineapp.vn/");
        //                    // mt.meta_file_url = mt.temp_download_url;

        //                    var uploadResponse = await _storageService.UploadFile(outStream, pfile, mt.meta_title);
        //                    mt.temp_download_url = uploadResponse.Url;
        //                    mt.meta_file_url = uploadResponse.Url;
        //                }
        //                //}
        //                //}
        //                //catch (Exception ex)
        //                //{
        //                //    Console.WriteLine("loi khong tao duoc file pdf: " + ex.Message);
        //                //    //outStream = wordToPdf.ConvertDocxToPdf(stream);
        //                //}
        //            }
        //            mt.meta_run_status = "Tạo tệp thành công";
        //        }
        //        else
        //        {
        //            mt.meta_run_status = "Không tìm thấy tệp mẫu";
        //            mt.meta_st = -1;
        //        }

        //    }
        //    //}
        //    //catch (Exception ex)
        //    //{
        //    //    Console.WriteLine(ex.Message);
        //    //}
        //    return input;
        //}

        public async Task<MemoryStream> CreateWordWithImages(List<string> imageUrls)
        {
            Document doc = new Document();
            Section section = doc.AddSection();
            foreach (var imageUrl in imageUrls)
            {
                try
                {
                    using (var webClient = new WebClient())
                    {
                        byte[] imageBytes = webClient.DownloadData(imageUrl);
                        using (MemoryStream ms = new MemoryStream(imageBytes))
                        {
                            Paragraph paragraph = section.AddParagraph();
                            DocPicture picture = paragraph.AppendPicture(ms);
                            picture.Width = 300;  // Đặt chiều rộng ảnh
                            picture.Height = 200; // Đặt chiều cao ảnh
                        }
                    }
                }
                catch (Exception ex)
                {
                    section.AddParagraph().AppendText($"Error loading image ({imageUrl}): {ex.Message}");
                }

                // Thêm dòng trống giữa các ảnh
                section.AddParagraph().AppendText("\n");
            }
            // Lưu tài liệu vào MemoryStream
            MemoryStream stream = new MemoryStream();
            doc.SaveToStream(stream, FileFormat.Docx);
            stream.Position = 0;
            return stream;
        }
    }
}