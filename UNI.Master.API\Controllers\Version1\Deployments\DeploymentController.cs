using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using UNI.Master.BLL.Interfaces.Deployments;
using UNI.Master.Model.Deployments;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1.Deployments
{
    /// <summary>
    /// 
    /// </summary>

    [Route("api/v1/[controller]/[action]")]
    [Authorize]
    [ApiController]
    public class DeploymentController : UniController
    {
        private readonly IDeploymentService _deploymentService;

        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="deploymentService"></param>
        /// <param name="logger"></param>
        public DeploymentController(IDeploymentService deploymentService, ILoggerFactory logger) : base(logger)
        {
            _deploymentService = deploymentService;
        }

        /// <summary>
        /// Dành cho webhook
        /// </summary>
        /// <param name="payload"></param>
        /// <param name="fromWebhook">đ<PERSON>h dấu được gọi từ webhook</param>
        /// <returns></returns>
        //[AllowAnonymous]
        [HttpPost("{fromWebhook}")]
        public async Task<IActionResult> SyncImage(object payload, bool? fromWebhook = false)
        {
            await _deploymentService.SyncImageAsync(payload, fromWebhook);
            return Ok();
        }

        /// <summary>
        /// Triển khai ứng dụng
        /// </summary>
        /// <param name="deployment"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> DeployApp([FromBody] ProductDeploymentDto deployment)
        {
            var app = new DeploymentAppDto
            {
                CustomerId = deployment.CustomerId,
                ProductId = deployment.ProductId,
            };
            var rs = await _deploymentService.DeployAppAsync(app);
            var rp = GetResponse(ApiResult.Success, rs.code, rs.messages);
            return rp;
        }
        [HttpPost]
        public async Task<IActionResult> GetTenants()
        {
            try
            {
                var result = await _deploymentService.GetTenantsAsync();
                var rp = GetResponse(ApiResult.Success, result);
                return Ok(rp);
                //throw new Exception("c");
            }
            catch (Exception e)
            {
                var kubeConfigDefaultLocation = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? Path.Combine(Environment.GetEnvironmentVariable("USERPROFILE") ?? "\\", ".kube\\config") : Path.Combine(Environment.GetEnvironmentVariable("HOME") ?? "/", ".kube/config");
                return Ok(kubeConfigDefaultLocation);

            }

        }
    }
}
