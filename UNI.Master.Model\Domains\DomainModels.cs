using System;
using UNI.Master.Model.Common;
using UNI.Model;

namespace UNI.Master.Model.Domains
{
    /// <summary>
    /// Domain filter for pagination and search
    /// </summary>
    public class DomainFilter : MasterFilterBase
    {
        /// <summary>
        /// Filter by domain type
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Filter by domain code
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Filter by domain name
        /// </summary>
        public string Name { get; set; }
    }

    /// <summary>
    /// Domain information model
    /// </summary>
    public class DomainInfo : MasterCommonInfo
    {
        /// <summary>
        /// Domain type
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Domain code (unique identifier)
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Domain name
        /// </summary>
        public string Name { get; set; }
    }

    /// <summary>
    /// Domain request model for API operations
    /// </summary>
    public class DomainRequestModel : FilterBase
    {
        /// <summary>
        /// Filter by domain type
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Filter by domain code
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Search term for name or code
        /// </summary>
        public string SearchTerm { get; set; }
    }


}
