using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.DAL.Interfaces;
using UNI.Master.Model.Common;
using UNI.Master.Model.Organizations;
using UNI.Model;

namespace UNI.Master.BLL.BusinessService
{
    public class OrganizationService:IOrganizationService
    {
        private readonly IOrganizationRepository _organizationRepository;
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="organizationRepository"></param>
        public OrganizationService(IOrganizationRepository organizationRepository)
        {
            _organizationRepository = organizationRepository;
        }

        public Task<CommonListPage> GetPageAsync(FilterOrganization query)
        {
            return _organizationRepository.GetPageAsync(query);
        }

        public Task<MasterCommonInfo> GetInfoAsync(System.Guid? id)
        {
            return _organizationRepository.GetInfoAsync(id);
        }

        public Task<BaseValidate> SetInfoAsync(MasterCommonInfo info)
        {
            return _organizationRepository.SetInfoAsync(info);
        }

        public Task<BaseValidate> DeleteAsync(System.Guid? id)
        {
            return _organizationRepository.DeleteAsync(id);
        }
    }
}
