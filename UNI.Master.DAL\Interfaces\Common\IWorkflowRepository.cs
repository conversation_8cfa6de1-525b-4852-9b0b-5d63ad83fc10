﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.DAL.Interfaces.Common
{

    public interface IWorkflowRepository
    {
        #region param-reg

        Task<List<CommonValue>> GetObjectList(string objKey);
        Task<CommonListPage> GetWorkflowPage(FilterBase filter);
        Task<userWorkFlowInfo> GetWorkflowInfo(Guid wft_id);
        Task<BaseValidate> SetWorkSubmit(uniWorkSubmit para);
        Task<BaseValidate> SetWorkApprove(uniWorkApprove para);
        Task<CommonListPage> GetParameterPage(FilterBase flt);
        Task<CommonViewInfo> GetInvParameter(long id);
        Task<BaseValidate> SetInvParameter(CommonViewInfo para);
        #endregion param-reg



    }
}
