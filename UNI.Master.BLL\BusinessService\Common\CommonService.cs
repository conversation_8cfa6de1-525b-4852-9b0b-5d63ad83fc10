﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Master.Model;
using UNI.Model;
using UNI.Model.Commons;
using UNI.Model.Core;

namespace UNI.Master.BLL.BusinessService.Common
{
    public class CommonService : ICommonService
    {
        private readonly ICommonRepository _repository;
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="repository"></param>
        public CommonService(ICommonRepository repository)
        {
            _repository = repository;
        }
        public Task<CommonViewInfo> GetManagerFilter(string table_key)
        {
            return _repository.GetManagerFilter(table_key);
        }
        public Task<List<CommonValue>> GetObjectList(string objKey)
        {
            return _repository.GetObjectList(objKey);
        }

        public Task<PaginationList<CommonValue>> GetSelectionListAsync(string key, string search,
            string refId)
        {
            return _repository.GetSelectionListAsync(key, search, refId);
        }

        public Task<List<CommonValue>> GetObjects(string objKey, string all, string filter)
        {
            return _repository.GetObjects(objKey, all, filter);
        }
        public Task<List<CommonValue>> GetCommonList(CommonInput common)
        {
            return _repository.GetCommonList(common);
        }

        public Task<List<CommonValue>> GetMonthList()
        {
            return _repository.GetMonthList();
        }
        public Task<List<CommonValue>> GetYearList()
        {
            return _repository.GetYearList();
        }
        public Task<BaseValidate> DelMultiCommons(CommonDeleteMulti Oids)
        {
            return _repository.DelMultiCommons(Oids);
        }

        public Task<CommonViewInfo> GetAddressDetail(Guid? Oid)
        {
            return _repository.GetAddressDetail(Oid);
        }
        //public Task<CommonViewInfo> GetAddressTax(AddressTax tax)
        //{
        //    return _repository.GetAddressTax(tax);
        //}
        public Task<CommonViewInfo> GetAddressDetailDraft(CommonViewInfo info)
        {
            return _repository.GetAddressDetailDraft(info);
        }
        public Task<List<CommonValue>> GetObjectClass(string objKey, string all)
        {
            return _repository.GetObjectClass(objKey, all);
        }
        public Task<List<CommonValue>> GetCountries(string filter, string resCode)
        {
            return _repository.GetCountries(filter, resCode);
        }
        public Task<List<CommonValue>> GetAddressStreets(AddressParam param)
        {
            return _repository.GetAddressStreets(param);
        }

        public Task<List<CommonValue>> GetOrganizeList(Guid? oid, int org_level, Guid? parentId, string all, string filter)
        {
            return _repository.GetOrganizeList(oid, org_level, parentId, all, filter);
        }
    }
}
