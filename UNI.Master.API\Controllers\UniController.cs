using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Api;
using UNI.Model;
using UNI.Model.Api;
using UNI.Utils;

namespace UNI.Master.API.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    /// <author>taint</author>
    /// <createdDate>2/2/2016</createdDate>
    /// <seealso>
    ///     <cref>System.Web.Http.ApiController</cref>
    /// </seealso>
    public class UniController : ControllerBase
    {
        /// <summary>
        /// logger
        /// </summary>
        protected readonly ILogger _logger;
        /// <summary>
        /// AppSettings
        /// </summary>
        protected readonly AppSettings _appSettings;

        /// <summary>
        /// StorageService
        /// </summary>
        protected readonly IApiStorageService _storageService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        /// <param name="storageService"></param>
        public UniController(IOptions<AppSettings> appSettings, ILoggerFactory logger,
            IApiStorageService storageService)
        {
            _appSettings = appSettings.Value;
            _logger = logger.CreateLogger(GetType().Name);
            _storageService = storageService;
            // _fileImportExportService = new FileImportExportService(_storageService, logger);
        }
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public UniController(IOptions<AppSettings> appSettings, ILoggerFactory logger)
        {
            _appSettings = appSettings.Value;
            _logger = logger.CreateLogger(GetType().Name);
        }

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="logger"></param>
        public UniController(ILoggerFactory logger)
        {
            _logger = logger.CreateLogger(GetType().Name);
        }

        /// <summary>
        /// Get Errors
        /// </summary>
        protected string Errors
        {
            get
            {
                try
                {
                    var sb = new StringBuilder();

                    foreach (var key in ModelState.Keys)
                    {
                        foreach (var error in ModelState[key]?.Errors)
                        {
                            sb.Append("Key: " + key + " - " + "Error: " + error.ErrorMessage + " @ " + error.Exception + "<br/>");
                        }
                    }

                    return sb.ToString();
                }
                catch (Exception e)
                {
                    _logger.LogError(e.StackTrace);
                    throw;
                }
            }
        }
        /// <summary>
        /// UserId
        /// </summary>
        protected string UserId
        {
            get
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId) && Request.Headers.TryGetValue("X-User-Id", out var headerUserId))
                {
                    userId = headerUserId.ToString();
                }

                return userId;
            }
        }
        /// <summary>
        /// UserName
        /// </summary>
        protected string UserName => User.Claims.Where(c => c.Type == "name").Select(c1 => c1.Value).FirstOrDefault();
        /// <summary>
        /// ClientId
        /// </summary>
        protected string ClientId => User.Claims.Where(c => c.Type == "client_id").Select(c1 => c1.Value).FirstOrDefault();
        /// <summary>
        /// Mã sản phâm
        /// </summary>
        protected string ProductCode => User.Claims.Where(c => c.Type == "product_code").Select(c1 => c1.Value).FirstOrDefault();
        /// <summary>
        /// Control Client
        /// </summary>
        public BaseCtrlClient CtrlClient
        {
            get
            {
                return new BaseCtrlClient
                {
                    ClientId = User.Claims.Where(c => c.Type == "client_id").Select(c1 => c1.Value).FirstOrDefault(),
                    ClientIp = Request.HttpContext.Connection.RemoteIpAddress?.ToString(),
                    hostUrl = HttpContext.Request.Scheme + "://" + HttpContext.Request.Host.Value,
                    UserId = User.Claims.Where(c => c.Type == "sub").Select(c1 => c1.Value).FirstOrDefault()
                };
            }
        }
        /// <summary>
        /// GetClaim
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        protected string GetClaim(string key)
        {
            var principal = HttpContext.User.Claims;
            return principal.Single(c => c.Type == key).Value;
        }

        /// <summary>
        /// ServiceHandler
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TX"></typeparam>
        /// <param name="records"></param>
        /// <returns></returns>
        protected delegate Task<ImportListPage> ServiceHandler<T, in TX>(TX records) where TX : BaseImportSet<T>;
        /// <summary>
        /// DoImportFile
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TX"></typeparam>
        /// <param name="file"></param>
        /// <param name="fromRow"></param>
        /// <param name="serviceHandler"></param>
        /// <returns></returns>
        protected async Task<BaseResponse<ImportListPage>> DoImportFile<T, TX>(IFormFile file, int fromRow,
            ServiceHandler<T, TX> serviceHandler) where T : new() where TX : BaseImportSet<T>, new()
        {
            var result = new BaseResponse<ImportListPage>();
            if (file == null || file.Length <= 0)
            {
                return GetErrorResponse<ImportListPage>(ApiResult.Error, 2, "Chưa có tệp được chọn");
            }

            if (!Path.GetExtension(file.FileName)!.Equals(".xlsx", StringComparison.OrdinalIgnoreCase) &&
                !Path.GetExtension(file.FileName)!.Equals(".xls", StringComparison.OrdinalIgnoreCase))
            {
                return GetErrorResponse<ImportListPage>(ApiResult.Error, 2,
                    "Định dạng tệp không được hỗ trợ, chỉ hỗ trợ tệp .xlsx hoặc .xls");
            }
            //DoCheckImportFile<T>(file,3);
            try
            {
                var workShiftRecords = new TX();
                using (var fs = new MemoryStream())
                {
                    await file.CopyToAsync(fs);
                    workShiftRecords.imports = FlexcellUtils.ReadToObject<T>(fs.ToArray(), fromRow);
                }

                // var fileUpload = await FireBaseServices.UploadFileCdn(file.OpenReadStream(), file.FileName, app: "s_hrm");
                var fileUpload = await _storageService.UploadFile(file);
                workShiftRecords.importFile = new uImportFile
                {
                    fileName = file.FileName,
                    fileSize = file.Length,
                    fileType = Path.GetExtension(file.FileName),
                    fileUrl = fileUpload.Url
                    // fileUrl = fileUpload.FilePath
                };
                var rs = await serviceHandler(workShiftRecords);
                //return GetResponse(ApiResult.Success, rs);
                if (rs.valid == false)
                {
                    return GetErrorResponse<ImportListPage>(ApiResult.Error, 2, rs.messages);
                }
                else
                {
                    return GetResponse<ImportListPage>(ApiResult.Success, rs);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, e.Message);
                result.AddError(ApiResult.Error, e.Message);
                result.SetStatus(ApiResult.Error);
                return result;
            }
        }
        /// <summary>
        /// GetResponse
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="status"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected static BaseResponse<T> GetResponse<T>(ApiResult status, T data)
        {
            return new BaseResponse<T>(status, data);
        }
        /// <summary>
        /// Get Response
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="status"></param>
        /// <param name="data"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        protected static BaseResponse<T> GetResponse<T>(ApiResult status, T data, string message)
        {
            var response = new BaseResponse<T>(status, data);
            response.SetStatus(status, message);
            return response;
        }
        /// <summary>
        /// GetErrorResponse
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="result"></param>
        /// <param name="code"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        protected BaseResponse<T> GetErrorResponse<T>(ApiResult result, int code, string message)
        {
            // trả mã lỗi
            var response = new BaseResponse<T>();

            // thêm lỗi
            response.AddError(message);
            response.SetStatus(result, message);
            response.SetStatus(code, message);

            return response;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="result"></param>
        /// <param name="message"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        protected BaseResponse<T> GetErrorResponse<T>(ApiResult result, string message)
        {
            // trả mã lỗi
            var response = new BaseResponse<T>();

            // thêm lỗi
            response.AddError(message);
            response.SetStatus(result, message);
            return response;
        }
        /// <summary>
        /// Accept-Language
        /// </summary>
        protected string AcceptLanguage => Request.Headers["Accept-Language"].ToString().Split(";").FirstOrDefault()?.Split(",").FirstOrDefault();
    }
}