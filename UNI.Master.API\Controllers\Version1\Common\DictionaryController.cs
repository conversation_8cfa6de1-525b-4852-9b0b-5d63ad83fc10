﻿using FlexCel.XlsAdapter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Api;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Model;
using UNI.Model.Api;
using UNI.Model.Commons;
using UNI.Utils;

namespace UNI.Master.API.Controllers.Version1.Common
{
    /// <summary>
    /// Dictionary API - Danh mục
    /// </summary>
    /// Author: duongpx
    /// CreateDate: 05/06/2024 11:35 AM
    /// <seealso cref="UniController" />
    [Route("api/v1/dictionary/[action]")]
    //[Authorize(AuthenticationSchemes = IdentityServerAuthenticationDefaults.AuthenticationScheme)]
    public class DictionaryController : UniController
    {
        private const int ImportRow3 = 3;
        private const int ImportRow5 = 5;

        /// <summary>
        /// The Dictionary service
        /// </summary>
        /// 19/10/2016 11:35 AM
        private readonly IDictionaryService _dicService;
        private readonly IMetaImportService _metaService;

        /// <summary>
        /// Initializes a new instance of the <see cref="DictionaryController"/> class.
        /// </summary>
        /// <param name="comService"></param>
        /// <param name="appSettings"></param>
        /// <param name="metaService"></param>
        /// <param name="storageService"></param>
        /// <param name="logger"></param>
        public DictionaryController(
            IDictionaryService comService,
            IOptions<AppSettings> appSettings,
            IMetaImportService metaService,
            IApiStorageService storageService,
            ILoggerFactory logger) : base(appSettings, logger, storageService)
        {
            _dicService = comService;
            _metaService = metaService;
        }
        /// <summary>
        /// GetDicFilter - List field lọc
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewInfo>> GetDicFilter()
        {
            var result = await _dicService.GetDicFilter();
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// GetDicTrees - Cây danh mục
        /// </summary>
        /// <param name="group_code"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<DicItem>>> GetDicTrees([FromQuery] string group_code, [FromQuery] string filter)
        {
            var result = await _dicService.GetDicTrees(group_code, filter);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// GetDicTrees - Cây danh mục
        /// </summary>
        /// <param name="group_code"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<DicItemTree>>> GetDicTreeNode([FromQuery] string group_code, [FromQuery] string filter)
        {
            if (string.IsNullOrEmpty(group_code)) { group_code = string.Empty; }
            var result = await _dicService.GetDicTreeNode(group_code, filter);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// GetDicPage - Trang danh sách 
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="filter"></param>
        /// <param name="app_st"></param>
        /// <param name="offSet"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetDicPage(
            [FromQuery] string tableName,
            [FromQuery] string filter,
            [FromQuery] int? app_st,
            [FromQuery] int? offSet,
            [FromQuery] int? pageSize
            )
        {
            var flt = new FilterBase(ClientId, UserId, offSet, pageSize, filter, app_st) { acceptLanguage = AcceptLanguage };
            var result = await _dicService.GetDicPage(flt, tableName);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        ///  Lấy thông tin TableName
        /// </summary>
        /// <param name="tableName">Tên bảng</param>
        /// <param name="Oid">Mã dữ liệu</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<DicInfo>> GetDicInfo(
            [FromQuery] string tableName,
            [FromQuery] Guid? Oid)
        {
            var result = await _dicService.GetDicInfo(tableName, Oid);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// SetDicInfo - Lưu thông tin
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<Guid?>> SetDicInfo([FromBody] DicInfo info)
        {
            var result = await _dicService.SetDicInfo(info);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.id, result.messages);
            }
            else
            {
                return GetErrorResponse<Guid?>(ApiResult.Error, 2, result.messages);
            }
        }
        /// <summary>
        /// DelDicInfo - Xóa thông tin
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="id"></param>
        /// <param name="Oid"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelDicInfo([FromQuery] string tableName, [FromQuery] Guid id, [FromQuery] Guid? Oid)
        {
            var result = await _dicService.DelDicInfo(tableName, id);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.messages);
            }
            else
            {
                return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
            }
        }
        /// <summary>
        /// DelMultiDicInfo - Xóa nhiều :
        /// </summary>
        /// <param name="commonDeleteMulti"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelMultiDicInfo([FromBody] CommonDeleteMulti commonDeleteMulti)
        {
            var result = await _dicService.DelMultiDicInfo(commonDeleteMulti);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.messages);
            }
            else
            {
                return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
            }
        }

        #region import & export excel
        /// <summary>
        /// SetDicImport
        /// </summary>
        /// <param name="meta"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ImportListPage>> SetDicImport([FromForm] DicFileSet meta)
        {
            if (!ModelState.IsValid)
            {
                return GetErrorResponse<ImportListPage>(ApiResult.Invalid, (int)ErrorCode.ModelInvalid, ErrorCode.ModelInvalid.GetDescription());
            }
            if (meta.file == null || meta.file.Length <= 0)
            {
                return GetErrorResponse<ImportListPage>(ApiResult.Invalid, 2, "Chưa có tệp được chọn");
            }
            if (meta.tableName == "bank_branches")
            {
                return await DoImportFile<DicImportV1, DicImportSetV1>(meta.file, 5,
                records => _dicService.SetDicImport(meta.tableName, records));
            }    
            using (var stream = new MemoryStream())
            {
                meta.file.CopyTo(stream);
                stream.Position = 0;

                XlsFile xls = new XlsFile();
                xls.Open(stream);

                int rowCount = xls.RowCount;
                int colCount = xls.ColCount;

                var records = new List<DicFieldItem>();

                // Đọc tên cột từ dòng số 4
                var fieldNames = new List<string>();
                for (int col = 1; col <= colCount; col++)
                {
                    fieldNames.Add(xls.GetStringFromCell(4, col));
                }

                // Đọc dữ liệu từ dòng số 5 trở đi
                for (int row = 5; row <= rowCount; row++)  // Bắt đầu từ dòng thứ 5
                {
                    for (int col = 1; col <= colCount; col++)
                    {
                        var dicFieldItem = new DicFieldItem
                        {
                            FieldName = fieldNames[col - 1],  // Lấy tên cột từ dòng số 4
                            RowId = (row-5).ToString(),
                            RowValue = xls.GetStringFromCell(row, col)
                        };
                        records.Add(dicFieldItem);
                    }
                }
                var rs = await _dicService.SetDicImport(records, meta.tableName, false);
                return GetResponse(ApiResult.Success, rs);
            }
        }
        /// <summary>
        /// SetDicImportAccept - Lưu import
        /// </summary>
        /// <param name="importSet"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<ImportListPage>> SetDicImportAccept([FromBody] DicImportSet importSet)
        {
            try
            {
                if (importSet.tableName == "bank_branches")
                {
                    var dicRerocds = new DicImportSetV1 { accept = importSet.accept, imports = new List<DicImportV1>() };
                    foreach (var item in importSet.imports)
                    {
                        var d = JsonConvert.DeserializeObject<DicImportV1>(item.ToString());
                        dicRerocds.imports.Add(d);
                        //dicRerocds.imports.Add(item as DicImportV1);
                    }
                    var drs = await _dicService.SetDicImport(importSet.tableName, dicRerocds);
                    return GetResponse(ApiResult.Success, drs);
                }
                else
                {
                    var records = new List<DicFieldItem>();
                    var rowId = 0;
                    foreach (var item in importSet.imports)
                    {
                        foreach (PropertyDescriptor pi in TypeDescriptor.GetProperties(item))
                        {
                            var selfValue = pi.GetValue(item);
                            var xit = new DicFieldItem { FieldName = pi.Name, RowId = rowId.ToString(), RowValue = selfValue.ToString() };
                            records.Add(xit);
                        }
                        rowId++;
                    }
                    var rs = await _dicService.SetDicImport(records, importSet.tableName, accept: true);
                    //await GenerateResultImportedFile(rs, ImportRow5);
                    return GetResponse(ApiResult.Success, rs);
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error: {Message}", e.Message);
                return GetErrorResponse<ImportListPage>(ApiResult.Error, 2, e.Message);
            }
        }
        
        /// <summary>
        /// SetDicExportDraft - Xuất
        /// </summary>
        /// <param name="importSet"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<FileStreamResult> SetDicExportDraft([FromBody] DicImportSet importSet)
        {
            var rs = await _dicService.SetDicExportDraft(importSet);
            return File(rs.Data, "application/octet-stream", "danh_sach.xlsx");
        }
        /// <summary>
        /// SetDicExport - Xuất danh sách
        /// </summary>
        /// <param name="tableName">Tên bảng</param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<FileStreamResult> SetDicExport([FromQuery] string tableName, [FromQuery] string filter)
        {
            var rs = await _dicService.SetDicExport(tableName, true, filter);
            return File(rs.Data, "application/octet-stream", $"{tableName}_{DateTime.Now.ToString("yyyy-MM-dd_hhmmss")}.xlsx");
        }
        /// <summary>
        /// GetDicImportTemp - Xuất file mẫu
        /// </summary>
        /// <param name="tableName">Tên bảng</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<FileStreamResult> GetDicImportTemp([FromQuery] string tableName)
        {
            var rs = await _dicService.SetDicExport(tableName, false, "");
            return File(rs.Data, "application/octet-stream", $"{tableName}_{DateTime.Now.ToString("yyyy-MM-dd_hhmmss")}.xlsx");
        }
        #endregion import & export excel
    }
}
