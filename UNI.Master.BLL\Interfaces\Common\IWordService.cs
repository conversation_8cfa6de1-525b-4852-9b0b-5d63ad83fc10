﻿using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces.Common
{
    public interface IWordService
    {
        Task<MetaTempResponse<ContractMeta>> CreateBill_MetaFiles(MetaTempResponse<ContractMeta> input);
        //Task<MetaTempResponse<ContractMeta>> CreateContract_MetaFiles(MetaTempResponse<ContractMeta> input);
        Task<MemoryStream> CreateWordWithImages(List<string> imageUrls);
    }
}
