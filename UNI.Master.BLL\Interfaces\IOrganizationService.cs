using System.Threading.Tasks;
using UNI.Master.Model.Organizations;
using UNI.Master.Model.Common;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces
{
    public interface IOrganizationService
    {
        Task<CommonListPage> GetPageAsync(FilterOrganization query);
        Task<MasterCommonInfo> GetInfoAsync(System.Guid? id);
        Task<BaseValidate> SetInfoAsync(MasterCommonInfo info);
        Task<BaseValidate> DeleteAsync(System.Guid? id);
    }
}
