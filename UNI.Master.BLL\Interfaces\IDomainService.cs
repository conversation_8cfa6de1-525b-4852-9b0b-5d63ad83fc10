using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.Model.Common;
using UNI.Master.Model.Domains;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces
{
    /// <summary>
    /// Domain service interface
    /// </summary>
    public interface IDomainService
    {
        /// <summary>
        /// Get paginated list of domains
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated domain list</returns>
        Task<CommonListPage> GetPageAsync(DomainFilter filter);

        /// <summary>
        /// Get domain information by ID
        /// </summary>
        /// <param name="id">Domain ID</param>
        /// <returns>Domain information</returns>
        Task<MasterCommonInfo> GetInfoAsync(Guid? id);

        /// <summary>
        /// Create or update domain information
        /// </summary>
        /// <param name="info">Domain information</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> SetInfoAsync(MasterCommonInfo info);

        /// <summary>
        /// Delete domain by ID
        /// </summary>
        /// <param name="id">Domain ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> DeleteAsync(Guid? id);


    }
}
