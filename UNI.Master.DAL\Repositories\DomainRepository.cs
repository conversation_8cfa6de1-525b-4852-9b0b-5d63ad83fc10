using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces;
using UNI.Master.Model.Common;
using UNI.Master.Model.Domains;
using UNI.Model;

namespace UNI.Master.DAL.Repositories
{
    /// <summary>
    /// Domain repository implementation
    /// </summary>
    public class DomainRepository : UniBaseRepository, IDomainRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="common">Common repository base</param>
        public DomainRepository(IUniCommonBaseRepository common) : base(common)
        {
        }

        /// <summary>
        /// Get paginated list of domains
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated domain list</returns>
        public Task<CommonListPage> GetPageAsync(DomainFilter filter)
        {
            const string storedProcedure = "sp_domain_page";
            return base.GetPageAsync(storedProcedure, filter, param =>
            {
                param.Add("@type", filter.Type);
                param.Add("@code", filter.Code);
                param.Add("@name", filter.Name);
                return param;
            });
        }

        /// <summary>
        /// Get domain information by ID
        /// </summary>
        /// <param name="id">Domain ID</param>
        /// <returns>Domain information</returns>
        public Task<MasterCommonInfo> GetInfoAsync(Guid? id)
        {
            const string storedProcedure = "sp_domain_fields";
            return base.GetFieldsAsync<MasterCommonInfo>(storedProcedure, new { id });
        }

        /// <summary>
        /// Create or update domain information
        /// </summary>
        /// <param name="info">Domain information</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> SetInfoAsync(MasterCommonInfo info)
        {
            const string storedProcedure = "sp_domain_set";
            return base.SetInfoAsync(storedProcedure, info, new { info.id });
        }

        /// <summary>
        /// Delete domain by ID
        /// </summary>
        /// <param name="id">Domain ID</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            const string storedProcedure = "sp_domain_delete";
            return base.DeleteAsync(storedProcedure, new { id });
        }


    }
}
