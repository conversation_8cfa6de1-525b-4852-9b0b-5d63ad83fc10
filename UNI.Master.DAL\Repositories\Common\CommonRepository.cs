﻿using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Master.Model;
using UNI.Model;
using UNI.Model.Commons;
using UNI.Model.Core;

namespace UNI.Master.DAL.Repositories.Common
{
    public class CommonRepository : UniBaseRepository, ICommonRepository
    {
        public CommonRepository(IUniCommonBaseRepository commonInfo) : base(commonInfo)
        {
        }
        public async Task<CommonViewInfo> GetManagerFilter(string table_key)
        {
            const string storedProcedure = "sp_common_filter_fields";
            return await base.GetFieldsAsync<CommonViewInfo>(storedProcedure, new { table_key });
        }
        public async Task<List<CommonValue>> GetObjectList(string objKey)
        {
            const string storedProcedure = "sp_config_object_data_get";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { objKey });
        }

        public async Task SetConfigData(string key, string value)
        {
            const string storedProcedure = "sp_config_data_set";
            var param = new { key, value };
            await base.ExecuteAsync(storedProcedure, param);
        }

        public async Task<PaginationList<CommonValue>> GetSelectionListAsync(string key, string search, string refId)
        {
            const string storedProcedure = "sp_common_selection_list";
            var param = new DynamicParameters();
            param.Add("key", key);
            param.Add("search", search);
            param.Add("refId", refId);
            param.Add("RecordsTotal", dbType: DbType.Int64, direction: ParameterDirection.Output);
            param.Add("RecordsFiltered", dbType: DbType.Int64, direction: ParameterDirection.Output);
            var list = await GetListAsync<CommonValue>(storedProcedure, param);
            var total = param.Get<long>("RecordsTotal");
            var totalFiltered = param.Get<long>("RecordsFiltered");
            // Lấy lại output param nếu cần, ở đây PaginationList cần tổng số bản ghi, nhưng base không trả về output param, nên chỉ trả về list
            // Nếu cần output param, phải custom lại base hoặc dùng QueryMultipleAsync
            return new PaginationList<CommonValue>(total, totalFiltered, list);
        }

        public async Task<List<CommonValue>> GetObjects(string objKey, string all, string filter)
        {
            const string storedProcedure = "sp_config_object_data_get";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { objKey, all, filter });
        }
        public async Task<List<CommonValue>> GetObjectClass(string objKey, string all)
        {
            const string storedProcedure = "sp_config_object_class_get";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { objKey, all });
        }
        public async Task<List<CommonValue>> GetCommonList(CommonInput common)
        {
            const string storedProcedure = "sp_common_list";
            return await base.GetListAsync<CommonValue>(storedProcedure, common);
        }
        public async Task<BaseValidate> DelMultiCommons(CommonDeleteMulti CommonDeleteMulti)
            => await base.DeleteAsync("sp_common_del",
           new
           {
               Oids = string.Join(",", CommonDeleteMulti.Ids.ToArray()),
               tableName = CommonDeleteMulti.TableName
           });
        public async Task<List<CommonValue>> GetMonthList()
        {
            const string storedProcedure = "sp_common_month_list";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { });
        }
        public async Task<List<CommonValue>> GetYearList()
        {
            const string storedProcedure = "sp_common_year_list";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { });
        }
        
        public async Task<CommonViewInfo> GetAddressDetail(Guid? Oid)
        {
            const string storedProcedure = "sp_common_address_detail_fields";
            return await base.GetFieldsAsync<CommonViewInfo>(storedProcedure, new { Oid });
        }
        public async Task<CommonViewInfo> GetAddressDetailDraft(CommonViewInfo info)
        {
            const string storedProcedure = "sp_common_address_detail_draft";
            return await base.GetFieldsAsync<CommonViewInfo>(storedProcedure, param =>
            {
                param.Add("@Oid", info.gd);
                param.AddDynamicParams(info.ToObject());
                return param;
            });
        }
        public async Task<List<CommonValue>> GetCountries(string filter, string resCode)
        {
            const string storedProcedure = "sp_common_country_list";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { resCode, filter });
        }
        public async Task<List<CommonValue>> GetAddressStreets(AddressParam param)
        {
            const string storedProcedure = "sp_common_address_detail_streets";
            return await base.GetListAsync<CommonValue>(storedProcedure, param);
        }

        public async Task<List<CommonValue>> GetOrganizeList(Guid? oid, int org_level, Guid? parentId, string all, string filter)
        {
            const string storedProcedure = "sp_organize_list";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { org_level, oid, parentId, filter, all });
        }
    }
}
