﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces
{
    public interface IProductPackageService
    {
        Task<CommonListPage> GetProductPackagePage(FilterProdPackage query);
        Task<productPackageInfo> GetProductPackageInfo(Guid? id);
        Task<BaseValidate> SetProductPackageInfo(productPackageInfo role);
        Task<BaseValidate> DelProductPackageInfo(Guid id);
        Task<List<CommonValue>> GetProductPackageList(string filter);


        Task<ProductPackageDetailInfo> GetProductPackageDetailInfo(Guid? id, Guid? productId);
        Task<BaseValidate> SetProductPackageDetailInfo(ProductPackageDetailInfo data);
        Task<List<CommonValue>> GetProductPackageDetailList(string filter);
        Task<BaseValidate> DelProductPackageDetailInfo(Guid id);
        Task<CommonListPage> GetProductPackageDetailsPage(FilterProdPackage flt);



        Task<ProductPackageDetailDescriptionInfo> GetProductPackageDetailDescriptionInfo(ProductPackageDetailDescriptionGet data);
        Task<BaseValidate> SetProductPackageDetailDescriptionInfo(ProductPackageDetailDescriptionInfo query);
        Task<BaseValidate> DelProductPackageDetailDescriptionInfo(Guid id);
        Task<CommonListPage> GetProductPackageDetailDescriptionList(FilterProductPackageDetailDescription flt);
    } 
}
