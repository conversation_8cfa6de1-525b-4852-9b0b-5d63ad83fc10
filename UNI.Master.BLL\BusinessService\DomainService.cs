using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.DAL.Interfaces;
using UNI.Master.Model.Common;
using UNI.Master.Model.Domains;
using UNI.Model;

namespace UNI.Master.BLL.BusinessService
{
    /// <summary>
    /// Domain service implementation
    /// </summary>
    public class DomainService : IDomainService
    {
        private readonly IDomainRepository _domainRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="domainRepository">Domain repository</param>
        public DomainService(IDomainRepository domainRepository)
        {
            _domainRepository = domainRepository;
        }

        /// <summary>
        /// Get paginated list of domains
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated domain list</returns>
        public Task<CommonListPage> GetPageAsync(DomainFilter filter)
        {
            return _domainRepository.GetPageAsync(filter);
        }

        /// <summary>
        /// Get domain information by ID
        /// </summary>
        /// <param name="id">Domain ID</param>
        /// <returns>Domain information</returns>
        public Task<MasterCommonInfo> GetInfoAsync(Guid? id)
        {
            return _domainRepository.GetInfoAsync(id);
        }

        /// <summary>
        /// Create or update domain information
        /// </summary>
        /// <param name="info">Domain information</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> SetInfoAsync(MasterCommonInfo info)
        {
            return _domainRepository.SetInfoAsync(info);
        }

        /// <summary>
        /// Delete domain by ID
        /// </summary>
        /// <param name="id">Domain ID</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            return _domainRepository.DeleteAsync(id);
        }


    }
}
