﻿using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Prodducts;
using UNI.Master.Model.Filters;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.DAL.Repositories.Products
{
    public class ProductModuleRepository : UniBaseRepository, IProductModuleRepository
    {
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="common"></param>
        public ProductModuleRepository(IUniCommonBaseRepository common) : base(common)
        {
        }

        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            return base.DeleteAsync("sp_product_module_delete", new { id });
        }

        public Task<ProductModuleInfo> GetInfoAsync(Guid? id, Guid? packageDetailId)
        {
            return base.GetFieldsAsync<ProductModuleInfo>("sp_product_module_fields", new { id, packageDetailId });
        }

        public Task<CommonListPage> GetPageAsync(FilterProductModule query)
        {
            return base.GetPageAsync("sp_product_module_page", query, objParams: new { packageDetailId = query.packageDetailId });
        }

        public Task<BaseValidate> SetInfoAsync(ProductModuleInfo info)
        {
            return base.SetInfoAsync("sp_product_module_set", info, new {info.id, info.packageDetailId});
        }
    }
}
