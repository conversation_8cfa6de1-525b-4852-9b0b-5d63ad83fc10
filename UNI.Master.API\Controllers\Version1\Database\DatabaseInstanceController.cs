using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using UNI.Master.BLL.Interfaces.Database;
using UNI.Master.Model.Database;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1.Database
{
    /// <summary>
    /// Database Instance Controller
    /// </summary>
    [Route("api/v1/database-instance/[action]")]
    [Authorize]
    public class DatabaseInstanceController : UniController
    {
        private readonly IDatabaseInstanceService _databaseInstanceService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="databaseInstanceService"></param>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public DatabaseInstanceController(
            IDatabaseInstanceService databaseInstanceService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _databaseInstanceService = databaseInstanceService;
        }

        /// <summary>
        /// Get Database Instance Page
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] DatabaseInstanceFilter filter)
        {
            var result = await _databaseInstanceService.GetPageAsync(filter);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get Database Instance Info
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<DatabaseInstanceInfo>> GetInfo([FromQuery] Guid? id)
        {
            var result = await _databaseInstanceService.GetInfoAsync(id);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Set Database Instance Info
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> SetInfo([FromBody] DatabaseInstanceInfo info)
        {
            var result = await _databaseInstanceService.SetInfoAsync(info);
            return GetResponse(ApiResult.Success, result.valid, result.messages);
        }

        /// <summary>
        /// Delete Database Instance
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<bool>> Delete([FromQuery] Guid? id)
        {
            var result = await _databaseInstanceService.DeleteAsync(id);
            return GetResponse(ApiResult.Success, result.valid, result.messages);
        }
    }
}
