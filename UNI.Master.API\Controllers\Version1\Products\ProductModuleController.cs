﻿using Elastic.Apm.Api.Kubernetes;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Products;
using UNI.Master.Model.Filters;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1.Products
{
    /// <summary>
    /// Module sản phẩm
    /// </summary>
    [Authorize]
    [Route("api/v1/[controller]/[action]")]
    [ApiController]
    public class ProductModuleController : UniController
    {
        private readonly IProductModuleService _productModuleService;
        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="productModuleService"></param>
        public ProductModuleController(ILoggerFactory logger, IProductModuleService productModuleService) : base(logger)
        {
            _productModuleService = productModuleService;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="query"></param>
        /// <example></example>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetProductModulePage([FromQuery] FilterProductModule query)
        {
            var result = await _productModuleService.GetPageAsync(query);
            var rp = GetResponse(ApiResult.Success, result);
            return rp;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="packageDetailId">packageDetailId</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<ProductModuleInfo>> GetProducModuleInfo([FromQuery] Guid? id, [FromQuery] Guid? packageDetailId)
        {
            var result = await _productModuleService.GetInfoAsync(id, packageDetailId);
            var rp = GetResponse(ApiResult.Success, result);
            return rp;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetProductModuleInfo([FromBody] ProductModuleInfo info)
        {
            var result = await _productModuleService.SetInfoAsync(info);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelProductModuleInfo([FromQuery] Guid? id)
        {
            var result = await _productModuleService.DeleteAsync(id);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
    }
}
