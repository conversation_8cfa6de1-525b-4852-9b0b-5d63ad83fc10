﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.BLL.Interfaces.Common
{
    /// <summary>
    /// Interface Investment Service
    /// <author>duongpx</author>
    /// <date>2020-04-47</date>
    /// </summary>
    public interface IWorkflowService
    {

        #region work-reg
        Task<List<CommonValue>> GetObjectList(string objKey);
        Task<CommonListPage> GetWorkflowPage(FilterBase filter);
        Task<userWorkFlowInfo> GetWorkflowInfo(Guid wft_id);
        Task<BaseValidate> SetWorkSubmit(uniWorkSubmit para);
        Task<BaseValidate> SetWorkApprove(uniWorkApprove para);
        Task<CommonListPage> GetParameterPage(FilterBase flt);
        Task<CommonViewInfo> GetInvParameter(long id);
        Task<BaseValidate> SetInvParameter(CommonViewInfo para);
        #endregion work-reg


    }
}
