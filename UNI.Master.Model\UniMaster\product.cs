﻿using UNI.Model;
using System;

namespace UNI.Master.Model.UniMaster
{

    
    public class productInfo : viewBaseInfo
    {
        public Guid? id { get; set; }
    }
    public class FilterProduct : FilterInput
    {
        public Guid? prod_line_id { get; set; }
        public Guid? customer_Id { get; set; }
        //public FilterProduct(string clientid, string userid, int? offset, int? pagesize, string filter, int gridwidth, Guid? prod_line_id, Guid? customer_Id) : base(clientid, userid, offset, pagesize, filter, gridwidth)
        //{
        //    this.prod_line_id = prod_line_id;
        //    this.customer_Id = customer_Id;
        //}
    }

    public class productPackageInfo : viewBaseInfo
    {
        public Guid? id { get; set; }
    }

    public class FilterProdPackage : FilterInput
    {
        public Guid? productId { get; set; }
        //public FilterProdPackage(string clientid, string userid, int? offset, int? pagesize, string filter, int gridwidth, Guid? prod_id) : base(clientid, userid, offset, pagesize, filter, gridwidth)
        //{
        //    this.prod_id = prod_id;
        //}
    }

    public class productFeatureInfo : viewBaseInfo
    {
        public Guid? id { get; set; }
        public Guid? productModuleId { set; get; }
    }

    public class ProductPackageDetailDescriptionInfo : viewBaseInfo
    {
        public Guid? id { get; set; }
        public Guid? packageDetailId { get; set; }
    }

    public class FilterProductPackageDetailDescription : FilterInput
    {
        public Guid? packageDetailId { get; set; }
    }

    public class ProductPackageDetailDescriptionGet
    {
        public Guid? id { get; set; }
        public Guid? packageDetailId { get; set; }
    }

    public class ProductPackageDetailInfo : viewBaseInfo
    {
        public Guid? id { get; set; }
        public Guid productId { get; set; }
    }

    public class FilterProductModule : FilterInput
    {
        public Guid? packageDetailId { get; set; }
    }

    public class FilterProductFeature : FilterInput
    {
        public Guid? productModuleId { get; set; }
    }
}
