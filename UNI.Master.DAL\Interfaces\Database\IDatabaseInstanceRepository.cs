using System;
using System.Threading.Tasks;
using UNI.Master.Model.Database;
using UNI.Model;

namespace UNI.Master.DAL.Interfaces.Database
{
    public interface IDatabaseInstanceRepository
    {
        Task<CommonListPage> GetPageAsync(DatabaseInstanceFilter filter);
        Task<DatabaseInstanceInfo> GetInfoAsync(Guid? id);
        Task<BaseValidate> SetInfoAsync(DatabaseInstanceInfo info);
        Task<BaseValidate> DeleteAsync(Guid? id);
    }
}
