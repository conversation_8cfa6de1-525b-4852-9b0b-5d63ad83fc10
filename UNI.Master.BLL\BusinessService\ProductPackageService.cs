﻿using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.DAL.Interfaces;
using UNI.Master.Model.Customers;

namespace UNI.Master.BLL.BusinessService
{
    public class ProductPackageService : IProductPackageService
    {
        private readonly IProductPackageRepository _productPackageRepository;
        public ProductPackageService(IProductPackageRepository productPackageRepository)
        {
            if (productPackageRepository != null)
                _productPackageRepository = productPackageRepository;
        }

        public Task<BaseValidate> DelProductPackageInfo(Guid id)
        {
            return _productPackageRepository.DelProductPackageInfo(id);
        }

        public Task<productPackageInfo> GetProductPackageInfo(Guid? id)
        {
            return _productPackageRepository.GetProductPackageInfo(id);
        }

        public Task<CommonListPage> GetProductPackagePage(FilterProdPackage query)
        {
            return _productPackageRepository.GetProductPackagePage(query);
        }

        public Task<BaseValidate> SetProductPackageInfo(productPackageInfo prod)
        {
            return _productPackageRepository.SetProductPackageInfo(prod);
        }
        public Task<List<CommonValue>> GetProductPackageList(string filter)
        {
            return _productPackageRepository.GetProductPackageList(filter);
        }


        public Task<ProductPackageDetailDescriptionInfo> GetProductPackageDetailDescriptionInfo(ProductPackageDetailDescriptionGet data)
        {
            return _productPackageRepository.GetProductPackageDetailDescriptionInfo(data);
        }
        public Task<BaseValidate> SetProductPackageDetailDescriptionInfo(ProductPackageDetailDescriptionInfo query)
        {
            return _productPackageRepository.SetProductPackageDetailDescriptionInfo(query);
        }
        public Task<BaseValidate> DelProductPackageDetailDescriptionInfo(Guid id)
        {
            return _productPackageRepository.DelProductPackageDetailDescriptionInfo(id);
        }
        public Task<CommonListPage> GetProductPackageDetailDescriptionList(FilterProductPackageDetailDescription flt)
        {
            return _productPackageRepository.GetProductPackageDetailDescriptionList(flt);
        }




        public Task<ProductPackageDetailInfo> GetProductPackageDetailInfo(Guid? id, Guid? productId)
        {
            return _productPackageRepository.GetProductPackageDetailInfo(id, productId);
        }

        public Task<BaseValidate> SetProductPackageDetailInfo(ProductPackageDetailInfo data)
        {
            return _productPackageRepository.SetProductPackageDetailInfo(data);
        }

        public Task<List<CommonValue>> GetProductPackageDetailList(string filter)
        {
            return _productPackageRepository.GetProductPackageDetailList(filter);
        }

        public Task<BaseValidate> DelProductPackageDetailInfo(Guid id)
        {
            return _productPackageRepository.DelProductPackageDetailInfo(id);
        }

        public Task<CommonListPage> GetProductPackageDetailsPage(FilterProdPackage flt)
        {
            return _productPackageRepository.GetProductPackageDetailsPage(flt);
        }
    }
}
