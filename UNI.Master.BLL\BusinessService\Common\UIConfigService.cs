﻿using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Master.DAL.Repositories;
using UNI.Master.Model;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.BLL.BusinessService.Common
{
    public class UIConfigService : IUIConfigService
    {
        private readonly IUiConfigRepository _configRepository;

        public UIConfigService(IUiConfigRepository repository)
        {
            _configRepository = repository;
        }

        public async Task<CommonListPage> GetFormViewPage(FilterInputTKey filter)
        {
            return await _configRepository.GetFormViewPage(filter);
        }

        public Task<BaseValidate> SetFormViewInfo(ConfigField para)
        {
            return _configRepository.SetFormViewInfo(para);
        }
        public Task<BaseValidate> DelFormViewInfo(long fieldId)
        {
            return _configRepository.DelFormViewInfo(fieldId);
        }
        public async Task<CommonListPage> GetGridViewPage(FilterInputGKey filter)
        {
            return await _configRepository.GetGridViewPage(filter);
        }

        public Task<BaseValidate> SetGridViewInfo(ConfigColumn para)
        {
            return _configRepository.SetGridViewInfo(para);
        }
        public Task<BaseValidate> DelGridViewInfo(long gridId)
        {
            return _configRepository.DelGridViewInfo(gridId);
        }
        public async Task<CommonViewInfo> GetGroupInfo(string key_1, string key_2)
        {
            return await _configRepository.GetGroupInfo(key_1, key_2);
        }
        public Task<BaseValidate> SetGroupInfo(CommonViewInfo para)
        {
            return _configRepository.SetGroupInfo(para);
        }
        public Task<Dashboard> ConfigDashBoardGet()
        {
            return _configRepository.ConfigDashBoardGet();
        }
        public Task<List<DarhBoardTimeLine>> GetTimeLinePage()
        {
            return _configRepository.GetTimeLinePage();
        }

        public Task<List<SelectItem<string>>> GetObjectAsync(string objectKey)
        {
            return _configRepository.GetObjectAsync(objectKey);
        }
    }
}
