﻿using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.Model;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.DAL.Interfaces.Common
{
    public interface IUiConfigRepository
    {
        Task<CommonListPage> GetFormViewPage(FilterInputTKey filter);
        Task<BaseValidate> SetFormViewInfo(ConfigField para);
        Task<BaseValidate> DelFormViewInfo(long fieldId);

        Task<CommonListPage> GetGridViewPage(FilterInputGKey filter);
        Task<BaseValidate> SetGridViewInfo(ConfigColumn para);
        Task<BaseValidate> DelGridViewInfo(long gridId);

        Task<CommonViewInfo> GetGroupInfo(string key_1, string key_2);
        Task<BaseValidate> SetGroupInfo(CommonViewInfo para);

        Task<Dashboard> ConfigDashBoardGet();
        Task<List<DarhBoardTimeLine>> GetTimeLinePage();
        Task<List<SelectItem<string>>> GetObjectAsync(string objectKey);
    }
}
