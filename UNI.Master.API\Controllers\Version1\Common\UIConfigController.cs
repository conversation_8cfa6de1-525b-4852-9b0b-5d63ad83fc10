﻿using System.Collections;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using UNI.Common;
using UNI.Master.Model;
using UNI.Model;
using UNI.Model.Api;
using UNI.Model.Commons;
using UNI.Utils;
using UNI.Master.BLL.Interfaces.Common;

namespace UNI.Master.API.Controllers.Version1.Common
{
    /// <summary>
    /// UInvConfigController
    /// </summary>
    //[Authorize]
    [Route("api/v1/config/[action]")]
    [Authorize]
    public class UIConfigController : UniController
    {
        private readonly IUIConfigService _uiconfigService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="service"></param>
        public UIConfigController(ILoggerFactory logger, IUIConfigService service) : base(logger)
        {
            _uiconfigService = service;
        }

        #region Configs
        /// <summary>
        /// Get Config Table view Page
        /// </summary>
        /// <param name="flt"></param>
        /// <returns></returns>
        /// 
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetFormViewPage(
            [FromQuery] FilterInputTKey flt)
        {
            flt.ucInput(UserId, ClientId, AcceptLanguage);
            var result = await _uiconfigService.GetFormViewPage(flt);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Set Configuration for field view
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        /// 
        [HttpPost]
        public async Task<BaseResponse<string>> SetFormViewInfo([FromBody] ConfigField para)
        {
            if (!ModelState.IsValid)
            {
                return GetErrorResponse<string>(ApiResult.Invalid, (int)ErrorCode.ModelInvalid, ErrorCode.ModelInvalid.GetDescription());
            }
            var result = await _uiconfigService.SetFormViewInfo(para);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.messages);
            }
            else
            {
                return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
            }
        }
        /// <summary>
        /// Delete Config Fields
        /// </summary>
        /// <param name="fieldId"></param>
        /// <returns></returns>
        /// 
        [HttpDelete]
        public async Task<BaseResponse<string>> DelFormViewInfo([FromQuery] long fieldId)
        {
            var result = await _uiconfigService.DelFormViewInfo(fieldId);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.messages);
            }
            else
            {
                return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
            }
        }
        /// <summary>
        /// Get Config Grid Page
        /// </summary>
        /// <param name="flt"></param>
        /// <returns></returns>
        /// 
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetGridViewPage(
            [FromQuery] FilterInputGKey flt)
        {
            flt.ucInput(UserId, ClientId, AcceptLanguage);
            var result = await _uiconfigService.GetGridViewPage(flt);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Set Configuration for Grid
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        /// 
        [HttpPost]
        public async Task<BaseResponse<string>> SetGridViewInfo([FromBody] ConfigColumn para)
        {
            if (!ModelState.IsValid)
            {
                return GetErrorResponse<string>(ApiResult.Invalid, (int)ErrorCode.ModelInvalid, ErrorCode.ModelInvalid.GetDescription());
            }
            var result = await _uiconfigService.SetGridViewInfo(para);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.messages);
            }
            else
            {
                return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
            }
        }
        /// <summary>
        /// Delete Config Grid
        /// </summary>
        /// <param name="gridId"></param>
        /// <returns></returns>
        /// 
        [HttpDelete]
        public async Task<BaseResponse<string>> DelGridViewInfo([FromQuery] long gridId)
        {
            var result = await _uiconfigService.DelGridViewInfo(gridId);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.messages);
            }
            else
            {
                return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
            }
        }

        #endregion Configs
        /// <summary>
        /// Get data Object
        /// </summary>
        /// <param name="objectKey"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("objects/{objectKey}")]
        public async Task<IActionResult> GetObject(string objectKey)
        {
            IEnumerable<SelectItem<string>> data = await _uiconfigService.GetObjectAsync(objectKey);
            var rp = GetResponse(ApiResult.Success, data);
            return Ok(rp);
        }

    }
}
