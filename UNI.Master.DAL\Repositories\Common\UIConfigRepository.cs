﻿using Dapper;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Master.Model;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.DAL.Repositories.Common
{
    public class UIConfigRepository : UniBaseRepository, IUiConfigRepository
    {

        public UIConfigRepository(IUniCommonBaseRepository commonInfo) : base(commonInfo)
        {
        }
        public async Task<CommonListPage> GetFormViewPage(FilterInputTKey filter)
        {
            const string storedProcedure = "sp_config_formview_page";
            return await GetPageAsync(storedProcedure, filter, new { table_name = filter.tableKey });
        }

        public async Task<BaseValidate> SetFormViewInfo(ConfigField para)
        {
            const string storedProcedure = "sp_config_formview_set";
            return await base.GetFirstOrDefaultAsync<BaseValidate>(storedProcedure, para);
        }
        public async Task<BaseValidate> DelFormViewInfo(long fieldId)
        {
            const string storedProcedure = "sp_config_formview_del";
            return await base.DeleteAsync(storedProcedure, new { id = fieldId });
        }
        public async Task<CommonListPage> GetGridViewPage(FilterInputGKey filter)
        {
            const string storedProcedure = "sp_config_gridview_page";
            return await GetPageAsync(storedProcedure, filter, new { view_grid = filter.gridKey });
        }
        public async Task<BaseValidate> SetGridViewInfo(ConfigColumn para)
        {
            const string storedProcedure = "sp_config_gridview_set";
            return await base.GetFirstOrDefaultAsync<BaseValidate>(storedProcedure, para);
        }

        public async Task<BaseValidate> DelGridViewInfo(long gridId)
        {
            const string storedProcedure = "sp_config_gridview_del";
            return await base.DeleteAsync(storedProcedure, new { id = gridId });
        }

        public async Task<CommonViewInfo> GetGroupInfo(string key_1, string key_2)
        {
            const string storedProcedure = "sp_config_group_get";
            return await base.GetFieldsAsync<CommonViewInfo>(storedProcedure, new { key_1, key_2 });
        }
        public async Task<BaseValidate> SetGroupInfo(CommonViewInfo para)
        {
            const string storedProcedure = "sp_config_group_set";
            return await base.SetInfoAsync<BaseValidate>(storedProcedure, para, new { para.id });
        }
        public async Task<Dashboard> ConfigDashBoardGet()
        {
            const string storedProcedure = "sp_dashboard_get";
            var rs = await base.GetMultipleAsync(storedProcedure, new { },
                result =>
                {
                    var data = new Dashboard();
                    data.view_client_web_grid = result.Read<viewGridFlex>().ToList();
                    data.client_web_dashboards = result.Read<object>().ToList();
                    data.view_user_access_grid = result.Read<viewGridFlex>().ToList();
                    data.user_access = result.Read<object>().ToList();
                    data.view_web_role_create_grid = result.Read<viewGridFlex>().ToList();
                    data.view_web_role_create = result.Read<object>().ToList();
                    return Task.FromResult(data);
                });
            return rs;
        }
        public async Task<List<DarhBoardTimeLine>> GetTimeLinePage()
        {
            const string storedProcedure = "sp_dashboard_timline_get";
            return await base.GetListAsync<DarhBoardTimeLine>(storedProcedure, new { });
        }

        public async Task<List<SelectItem<string>>> GetObjectAsync(string objectKey)
        {
            const string storedProcedure = "sp_sys_config_data_get";
            return await base.GetListAsync<SelectItem<string>>(storedProcedure, new { });
        }


    }
}
