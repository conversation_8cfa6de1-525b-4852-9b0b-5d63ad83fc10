﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Model;
using UNI.Model.Api;
using UNI.Model.Commons;

namespace UNI.Master.API.Controllers.Version1.Common
{

    /// <summary>
    /// Investment Api for web
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 2020-04-20 9:31 AM
    /// <seealso cref="UniController" />
    [Route("api/v1/workflow/[action]")]
    [Authorize]
    public class WorkflowController : UniController
    {
        #region instance-reg
        private readonly IWorkflowService _kinvService;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="WorkflowController"/> class.
        /// </summary>
        /// <param name="kinvService"></param>
        /// <param name="mapper"></param>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public WorkflowController(
            IWorkflowService kinvService,
            IMapper mapper,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _kinvService = kinvService;
            _mapper = mapper;
        }
        #endregion instance-reg

        #region param-reg
        /// <summary>
        /// Get Parameter Page
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="gridWidth"></param>
        /// <param name="offSet"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        /// 
        //[ServiceFilter(typeof(AuditFilterAttribute))]
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetParameterPage([FromQuery] string filter, [FromQuery] int gridWidth,
            [FromQuery] int? offSet, [FromQuery] int? pageSize)
        {
            var flt = new FilterBase(ClientId, UserId, offSet, pageSize, filter, gridWidth);
            var result = await _kinvService.GetParameterPage(flt);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// Set Parameter
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        /// 
        //[ServiceFilter(typeof(AuditFilterAttribute))]
        [HttpPost]
        public async Task<BaseResponse<string>> SetParameter([FromBody] CommonViewInfo para)
        {
            var result = await _kinvService.SetInvParameter(para);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.messages);
            }
            else
            {
                return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
            }
        }
        /// <summary>
        /// Get Inv Parameter
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// 
        //[ServiceFilter(typeof(AuditFilterAttribute))]
        [HttpGet]
        public async Task<BaseResponse<CommonViewInfo>> GetParameter([FromQuery] long id)
        {
            var result = await _kinvService.GetInvParameter(id);
            return GetResponse(ApiResult.Success, result);
        }

        #endregion param

        #region workflow
        /// <summary>
        /// Get Work flow Page
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="gridWidth"></param>
        /// <param name="offSet"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        /// 
        //[ServiceFilter(typeof(AuditFilterAttribute))]
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetWorkflowPage(
            [FromQuery] string filter,
            [FromQuery] int gridWidth,
            [FromQuery] int? offSet,
            [FromQuery] int? pageSize)
        {
            var flt = new FilterBase(ClientId, UserId, offSet, pageSize, filter, gridWidth);
            var result = await _kinvService.GetWorkflowPage(flt);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// Get Work flow Info
        /// </summary>
        /// <param name="wft_id"></param>
        /// <returns></returns>
        /// 
        //[ServiceFilter(typeof(AuditFilterAttribute))]
        [HttpGet]
        public async Task<BaseResponse<userWorkFlowInfo>> GetWorkflowInfo([FromQuery] Guid wft_id)
        {
            var result = await _kinvService.GetWorkflowInfo(wft_id);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Set Work Approve
        /// </summary>
        /// <param name="para"></param>
        /// <returns></returns>
        /// 
        //[ServiceFilter(typeof(AuditFilterAttribute))]
        [HttpPut]
        public async Task<BaseResponse<string>> SetWorkApprove([FromBody] uniWorkApprove para)
        {
            var result = await _kinvService.SetWorkApprove(para);
            if (result.valid)
            {
                return GetResponse(ApiResult.Success, result.messages);
            }
            else
            {
                return GetErrorResponse<string>(ApiResult.Error, 2, result.messages);
            }
        }
        #endregion parma-reg
    }
}
