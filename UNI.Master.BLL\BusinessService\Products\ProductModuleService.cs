﻿using System;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Products;
using UNI.Master.DAL.Interfaces.Prodducts;
using UNI.Master.Model.Filters;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.BLL.BusinessService.Products
{
    public class ProductModuleService : IProductModuleService
    {
        private readonly IProductModuleRepository _productModuleRepository;
        public ProductModuleService(IProductModuleRepository productModuleRepository)
        {
            _productModuleRepository = productModuleRepository;
        }
        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            return _productModuleRepository.DeleteAsync(id);
        }

        public Task<ProductModuleInfo> GetInfoAsync(Guid? id, Guid? packageDetailId)
        {
            return _productModuleRepository.GetInfoAsync(id, packageDetailId);
        }

        public Task<CommonListPage> GetPageAsync(FilterProductModule query)
        {
            return _productModuleRepository.GetPageAsync(query);
        }

        public Task<BaseValidate> SetInfoAsync(ProductModuleInfo info)
        {
            return _productModuleRepository.SetInfoAsync(info);
        }
    }
}
