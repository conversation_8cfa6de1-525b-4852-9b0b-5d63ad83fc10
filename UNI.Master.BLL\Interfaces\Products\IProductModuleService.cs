﻿using System;
using System.Threading.Tasks;
using UNI.Master.Model.Filters;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces.Products
{
    public interface IProductModuleService
    {
        Task<CommonListPage> GetPageAsync(FilterProductModule query);
        Task<ProductModuleInfo> GetInfoAsync(Guid? id, Guid? packageDetailId);
        Task<BaseValidate> SetInfoAsync(ProductModuleInfo info);
        Task<BaseValidate> DeleteAsync(Guid? id);
    }
}
