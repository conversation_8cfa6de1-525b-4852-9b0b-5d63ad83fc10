using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.Model.Deployments;
using UNI.Model;

namespace UNI.Master.DAL.Interfaces.Deployments
{
    /// <summary>
    /// Deployment Database Repository Interface
    /// </summary>
    public interface IDeploymentDatabaseRepository
    {
        /// <summary>
        /// Get deployment databases page
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of deployment databases</returns>
        Task<CommonListPage> GetPageAsync(DeploymentDatabaseFilter filter);

        /// <summary>
        /// Get deployment database info by ID
        /// </summary>
        /// <param name="id">Database ID</param>
        /// <returns>Database information</returns>
        Task<DeploymentDatabaseInfo> GetInfoAsync(Guid? id);

        /// <summary>
        /// Create or update deployment database
        /// </summary>
        /// <param name="info">Database information</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> SetInfoAsync(DeploymentDatabaseInfo info);

        /// <summary>
        /// Delete deployment database
        /// </summary>
        /// <param name="id">Database ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> DeleteAsync(Guid? id);

        /// <summary>
        /// Create deployment database for customer and product
        /// </summary>
        /// <param name="request">Create request</param>
        /// <returns>Validation result with database ID</returns>
        Task<BaseValidate<Guid?>> CreateDeploymentDatabaseAsync(CreateDeploymentDatabaseRequest request);

        /// <summary>
        /// Update deployment database
        /// </summary>
        /// <param name="request">Update request</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> UpdateDeploymentDatabaseAsync(UpdateDeploymentDatabaseRequest request);

        /// <summary>
        /// Get deployment databases by customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="productId">Optional product ID filter</param>
        /// <returns>List of deployment databases</returns>
        Task<IEnumerable<DeploymentDatabaseDto>> GetByCustomerAsync(Guid customerId, Guid? productId = null);

        /// <summary>
        /// Get deployment databases by deployment
        /// </summary>
        /// <param name="deploymentId">Deployment ID</param>
        /// <returns>List of deployment databases</returns>
        Task<IEnumerable<DeploymentDatabaseDto>> GetByDeploymentAsync(Guid deploymentId);

        /// <summary>
        /// Initialize database schema
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <param name="versionId">Schema version ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> InitializeDatabaseSchemaAsync(Guid databaseId, Guid versionId);

        /// <summary>
        /// Migrate database schema
        /// </summary>
        /// <param name="request">Migration request</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> MigrateDatabaseSchemaAsync(DatabaseSchemaMigrationRequest request);

        /// <summary>
        /// Check database connection
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> CheckConnectionAsync(Guid databaseId);

        /// <summary>
        /// Update database status
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <param name="status">New status</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> UpdateStatusAsync(Guid databaseId, string status);

        /// <summary>
        /// Get database statistics
        /// </summary>
        /// <param name="customerId">Optional customer ID filter</param>
        /// <param name="productId">Optional product ID filter</param>
        /// <returns>Database statistics</returns>
        Task<object> GetStatisticsAsync(Guid? customerId = null, Guid? productId = null);
    }
}
