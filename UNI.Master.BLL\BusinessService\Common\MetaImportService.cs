﻿using System;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Model;

namespace UNI.Master.BLL.BusinessService.Common
{
    /// <summary>
    /// Class WorktimeService.
    /// <author>TH</author>
    /// <date>2015/12/02</date>
    /// </summary>
    public class MetaImportService : IMetaImportService
    {
        private readonly IMetaImportRepository _posRepository;
        public MetaImportService(
            IMetaImportRepository posRepository
            )
        {
            if (posRepository != null)
                _posRepository = posRepository;
        }

        public async Task<BaseValidate> DelImport(Guid impId)
        {
            return await _posRepository.DelImport(impId);
        }

        public Task<CommonListPage> GetImportPage(FilterInput flt, string import_type)
        {
            return _posRepository.GetImportPage(flt, import_type);
        }
    }
}
