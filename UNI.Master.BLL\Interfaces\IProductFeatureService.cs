﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces
{
    public interface IProductFeatureService
    {
        Task<CommonListPage> GetProductFeaturePage(FilterProductFeature flt);
        Task<productFeatureInfo> GetProductFeatureInfo(Guid? id, Guid? productModuleId);
        Task<BaseValidate> SetProductFeatureInfo(productFeatureInfo query);
        Task<BaseValidate> DelProductFeatureInfo(Guid id);
        Task<List<CommonValue>> GetProductFeatureList(string filter, string productModuleId);
    }
}
