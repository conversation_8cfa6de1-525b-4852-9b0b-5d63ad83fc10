06/08/2025 19:28:53 [DBG] Hosting starting
06/08/2025 19:28:53 [INF] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-0b3a7ebf-bc38-479a-a1f0-e4818574d695.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-277c0694-b6f9-44e3-93d6-68bae4ae5e5a.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-2e748a85-f3a4-4e36-8587-14fd29ee2fb8.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-30c35d9f-d57c-43b8-ab14-fac4f5e6798d.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-430dd5e8-d662-4a6e-b84e-698c72b961d0.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-56d0fdd7-476f-44f2-aaad-fe2f34ace553.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-5d6d6055-9d04-466a-82a1-92473420c6a3.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-697d5bac-49ff-46d8-9eda-81ede095f29b.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-a44694ab-7545-4489-9933-081816257a27.xml"'.
06/08/2025 19:28:53 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-f361c314-d199-42bf-9856-a4772c2786eb.xml"'.
06/08/2025 19:28:53 [DBG] Found key {0b3a7ebf-bc38-479a-a1f0-e4818574d695}.
06/08/2025 19:28:53 [DBG] Found key {277c0694-b6f9-44e3-93d6-68bae4ae5e5a}.
06/08/2025 19:28:53 [DBG] Found key {2e748a85-f3a4-4e36-8587-14fd29ee2fb8}.
06/08/2025 19:28:53 [DBG] Found key {30c35d9f-d57c-43b8-ab14-fac4f5e6798d}.
06/08/2025 19:28:53 [DBG] Found key {430dd5e8-d662-4a6e-b84e-698c72b961d0}.
06/08/2025 19:28:53 [DBG] Found key {56d0fdd7-476f-44f2-aaad-fe2f34ace553}.
06/08/2025 19:28:53 [DBG] Found key {5d6d6055-9d04-466a-82a1-92473420c6a3}.
06/08/2025 19:28:53 [DBG] Found key {697d5bac-49ff-46d8-9eda-81ede095f29b}.
06/08/2025 19:28:53 [DBG] Found key {a44694ab-7545-4489-9933-081816257a27}.
06/08/2025 19:28:53 [DBG] Found key {f361c314-d199-42bf-9856-a4772c2786eb}.
06/08/2025 19:28:53 [DBG] Considering key {56d0fdd7-476f-44f2-aaad-fe2f34ace553} with expiration date 2025-10-11 13:50:34Z as default key.
06/08/2025 19:28:53 [DBG] Forwarded activator type request from "Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60" to "Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60"
06/08/2025 19:28:53 [DBG] Decrypting secret element using Windows DPAPI.
06/08/2025 19:28:53 [DBG] Forwarded activator type request from "Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60" to "Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60"
06/08/2025 19:28:53 [DBG] Opening CNG algorithm '"AES"' from provider 'null' with chaining mode CBC.
06/08/2025 19:28:53 [DBG] Opening CNG algorithm '"SHA256"' from provider 'null' with HMAC.
06/08/2025 19:28:53 [DBG] Using key {56d0fdd7-476f-44f2-aaad-fe2f34ace553} as the default key.
06/08/2025 19:28:53 [DBG] Key ring with default key {56d0fdd7-476f-44f2-aaad-fe2f34ace553} was loaded during application startup.
06/08/2025 19:28:54 [DBG] Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
06/08/2025 19:28:54 [DBG] Failed to locate the development https certificate at '"C:\Users\<USER>\AppData\Roaming\ASP.NET\https\UNI.Master.API.pfx"'.
06/08/2025 19:28:54 [INF] Now listening on: "http://localhost:3103"
06/08/2025 19:28:54 [DBG] Loaded hosting startup assembly "UNI.Master.API"
06/08/2025 19:28:54 [INF] Application started. Press Ctrl+C to shut down.
06/08/2025 19:28:54 [INF] Hosting environment: "Development"
06/08/2025 19:28:54 [INF] Content root path: "D:\Workspace\Unicloud\Backend\uni-master\UNI.Master.API"
06/08/2025 19:28:54 [DBG] Hosting started
06/08/2025 19:28:54 [DBG] Connection id ""0HNEKSJ7EPQFT"" accepted.
06/08/2025 19:28:54 [DBG] Connection id ""0HNEKSJ7EPQFT"" started.
06/08/2025 19:28:54 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger""" - null null
06/08/2025 19:28:54 [DBG] Wildcard detected, all requests with hosts will be allowed.
06/08/2025 19:28:54 [WRN] Failed to determine the https port for redirect.
06/08/2025 19:28:54 [DBG] The request path "/swagger" does not match a supported file type
06/08/2025 19:28:54 [DBG] No candidates found for the request path '"/swagger"'
06/08/2025 19:28:54 [DBG] Request did not match any endpoints
06/08/2025 19:28:54 [DBG] Connection id ""0HNEKSJ7EPQFT"" completed keep alive response.
06/08/2025 19:28:54 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger""" - 302 0 null 94.0755ms
06/08/2025 19:28:54 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/index.html""" - null null
06/08/2025 19:28:54 [DBG] The request path "/swagger/index.html" does not match an existing file
06/08/2025 19:28:54 [DBG] No candidates found for the request path '"/swagger/index.html"'
06/08/2025 19:28:54 [DBG] Request did not match any endpoints
06/08/2025 19:28:54 [DBG] Connection id ""0HNEKSJ7EPQFT"" completed keep alive response.
06/08/2025 19:28:54 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/index.html""" - 200 null "text/html; charset=utf-8" 123.2578ms
06/08/2025 19:28:55 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/v1/swagger.json""" - null null
06/08/2025 19:28:55 [DBG] The request path "/swagger/v1/swagger.json" does not match an existing file
06/08/2025 19:28:55 [DBG] No candidates found for the request path '"/swagger/v1/swagger.json"'
06/08/2025 19:28:55 [DBG] Request did not match any endpoints
06/08/2025 19:28:57 [DBG] Connection id ""0HNEKSJ7EPQFT"" completed keep alive response.
06/08/2025 19:28:57 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/v1/swagger.json""" - 200 null "application/json; charset=utf-8" 1072.5023ms
06/08/2025 19:29:14 [DBG] Connection id ""0HNEKSJ7EPQFT"" received FIN.
06/08/2025 19:29:14 [DBG] Connection id ""0HNEKSJ7EPQFT"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
06/08/2025 19:29:14 [DBG] Connection id ""0HNEKSJ7EPQFT"" disconnecting.
06/08/2025 19:29:14 [DBG] Connection id ""0HNEKSJ7EPQFT"" stopped.
