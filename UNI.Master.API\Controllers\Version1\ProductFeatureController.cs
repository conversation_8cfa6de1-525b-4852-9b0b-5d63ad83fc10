﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1
{
    /// <summary>
    /// ProductFeature Controller
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 07/04/2020 9:31 AM
    /// <seealso cref="ProductFeatureController" />
    [Route("api/v1/productFeature/[action]")]
    [Authorize]
    public class ProductFeatureController : UniController
    {
        private readonly IProductFeatureService _productFeatureService;
        private readonly ICommonService _systemService;


        /// <summary>
        /// Web Controller
        /// </summary>
        /// <param name="productFeatureService"></param>
        /// <param name="systemService"></param>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public ProductFeatureController(
            IProductFeatureService productFeatureService,
            ICommonService systemService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _productFeatureService = productFeatureService;
            _systemService = systemService;
        }
        /// <summary>
        /// GetProductFeaturePage - Danh sách tính năng sp
        /// </summary>
        /// <param name="flt"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetProductFeaturePage(
            [FromQuery] FilterProductFeature flt)
        {
            try
            {
                flt.ucInput(this.UserId, this.ClientId, this.AcceptLanguage);
                var rs = await _productFeatureService.GetProductFeaturePage(flt);
                var rp = GetResponse(ApiResult.Success, rs);
                return rp;
            }
            catch (Exception e)
            {
                _logger.LogError($"{e}");
                var rp = new BaseResponse<CommonListPage>(ApiResult.Error, e.Message);
                return rp;
            }
        }
        /// <summary>
        /// GetProductFeatureInfo - Lấy thông tin tính năng sp
        /// </summary>
        /// <param name="id"></param>
        /// <param name="productModuleId"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<productFeatureInfo>> GetProductFeatureInfo([FromQuery] Guid? id, Guid? productModuleId)
        {
            var result = await _productFeatureService.GetProductFeatureInfo(id, productModuleId);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// SetProductFeatureInfo - Thêm/sửa thông tin tính năng
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetProductFeatureInfo([FromBody] productFeatureInfo prod)
        {
            var result = await _productFeatureService.SetProductFeatureInfo(prod);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// DelProductFeatureInfo - Xóa tính năng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelProductFeatureInfo([FromQuery] Guid id)
        {
            var result = await _productFeatureService.DelProductFeatureInfo(id);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// GetProductFeatureList - Danh mục tính năng
        /// </summary>
        /// <param name="filter"> Tìm theo tên tính năng</param>
        /// <param name="productModuleId"> Tìm theo product id</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetProductFeatureList([FromQuery] string filter, string productModuleId)
        {
            var result = await _productFeatureService.GetProductFeatureList(filter, productModuleId);
            return GetResponse(ApiResult.Success, result);
        }
    }
}
