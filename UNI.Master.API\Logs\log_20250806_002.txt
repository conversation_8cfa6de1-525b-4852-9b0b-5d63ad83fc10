06/08/2025 15:18:16 [DBG] Hosting starting
06/08/2025 15:18:16 [INF] User profile is available. Using '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys"' as key repository and Windows DPAPI to encrypt keys at rest.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-0b3a7ebf-bc38-479a-a1f0-e4818574d695.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-277c0694-b6f9-44e3-93d6-68bae4ae5e5a.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-2e748a85-f3a4-4e36-8587-14fd29ee2fb8.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-30c35d9f-d57c-43b8-ab14-fac4f5e6798d.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-430dd5e8-d662-4a6e-b84e-698c72b961d0.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-56d0fdd7-476f-44f2-aaad-fe2f34ace553.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-5d6d6055-9d04-466a-82a1-92473420c6a3.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-697d5bac-49ff-46d8-9eda-81ede095f29b.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-a44694ab-7545-4489-9933-081816257a27.xml"'.
06/08/2025 15:18:16 [DBG] Reading data from file '"C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys\key-f361c314-d199-42bf-9856-a4772c2786eb.xml"'.
06/08/2025 15:18:16 [DBG] Found key {0b3a7ebf-bc38-479a-a1f0-e4818574d695}.
06/08/2025 15:18:16 [DBG] Found key {277c0694-b6f9-44e3-93d6-68bae4ae5e5a}.
06/08/2025 15:18:16 [DBG] Found key {2e748a85-f3a4-4e36-8587-14fd29ee2fb8}.
06/08/2025 15:18:16 [DBG] Found key {30c35d9f-d57c-43b8-ab14-fac4f5e6798d}.
06/08/2025 15:18:16 [DBG] Found key {430dd5e8-d662-4a6e-b84e-698c72b961d0}.
06/08/2025 15:18:16 [DBG] Found key {56d0fdd7-476f-44f2-aaad-fe2f34ace553}.
06/08/2025 15:18:16 [DBG] Found key {5d6d6055-9d04-466a-82a1-92473420c6a3}.
06/08/2025 15:18:16 [DBG] Found key {697d5bac-49ff-46d8-9eda-81ede095f29b}.
06/08/2025 15:18:16 [DBG] Found key {a44694ab-7545-4489-9933-081816257a27}.
06/08/2025 15:18:16 [DBG] Found key {f361c314-d199-42bf-9856-a4772c2786eb}.
06/08/2025 15:18:16 [DBG] Considering key {56d0fdd7-476f-44f2-aaad-fe2f34ace553} with expiration date 2025-10-11 13:50:34Z as default key.
06/08/2025 15:18:16 [DBG] Forwarded activator type request from "Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60" to "Microsoft.AspNetCore.DataProtection.XmlEncryption.DpapiXmlDecryptor, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60"
06/08/2025 15:18:16 [DBG] Decrypting secret element using Windows DPAPI.
06/08/2025 15:18:16 [DBG] Forwarded activator type request from "Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60" to "Microsoft.AspNetCore.DataProtection.AuthenticatedEncryption.ConfigurationModel.AuthenticatedEncryptorDescriptorDeserializer, Microsoft.AspNetCore.DataProtection, Culture=neutral, PublicKeyToken=adb9793829ddae60"
06/08/2025 15:18:16 [DBG] Opening CNG algorithm '"AES"' from provider 'null' with chaining mode CBC.
06/08/2025 15:18:16 [DBG] Opening CNG algorithm '"SHA256"' from provider 'null' with HMAC.
06/08/2025 15:18:16 [DBG] Using key {56d0fdd7-476f-44f2-aaad-fe2f34ace553} as the default key.
06/08/2025 15:18:16 [DBG] Key ring with default key {56d0fdd7-476f-44f2-aaad-fe2f34ace553} was loaded during application startup.
06/08/2025 15:18:16 [DBG] Registered model binder providers, in the following order: ["Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BinderTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ServicesModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.BodyModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.HeaderModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FloatingPointTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.EnumTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DateTimeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.SimpleTypeModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.TryParseModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CancellationTokenModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ByteArrayModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormFileModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.FormCollectionModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.KeyValuePairModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.DictionaryModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ArrayModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.CollectionModelBinderProvider", "Microsoft.AspNetCore.Mvc.ModelBinding.Binders.ComplexObjectModelBinderProvider"]
06/08/2025 15:18:17 [DBG] Failed to locate the development https certificate at '"C:\Users\<USER>\AppData\Roaming\ASP.NET\https\UNI.Master.API.pfx"'.
06/08/2025 15:18:17 [INF] Now listening on: "http://localhost:3103"
06/08/2025 15:18:17 [DBG] Loaded hosting startup assembly "UNI.Master.API"
06/08/2025 15:18:17 [INF] Application started. Press Ctrl+C to shut down.
06/08/2025 15:18:17 [INF] Hosting environment: "Development"
06/08/2025 15:18:17 [INF] Content root path: "D:\Workspace\Unicloud\Backend\uni-master\UNI.Master.API"
06/08/2025 15:18:17 [DBG] Hosting started
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000NB"" accepted.
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000NB"" started.
06/08/2025 15:18:17 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger""" - null null
06/08/2025 15:18:17 [DBG] Wildcard detected, all requests with hosts will be allowed.
06/08/2025 15:18:17 [WRN] Failed to determine the https port for redirect.
06/08/2025 15:18:17 [DBG] The request path "/swagger" does not match a supported file type
06/08/2025 15:18:17 [DBG] No candidates found for the request path '"/swagger"'
06/08/2025 15:18:17 [DBG] Request did not match any endpoints
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000NB"" completed keep alive response.
06/08/2025 15:18:17 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger""" - 302 0 null 115.4856ms
06/08/2025 15:18:17 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/index.html""" - null null
06/08/2025 15:18:17 [DBG] The request path "/swagger/index.html" does not match an existing file
06/08/2025 15:18:17 [DBG] No candidates found for the request path '"/swagger/index.html"'
06/08/2025 15:18:17 [DBG] Request did not match any endpoints
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000NB"" completed keep alive response.
06/08/2025 15:18:17 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/index.html""" - 200 null "text/html; charset=utf-8" 135.7305ms
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000NC"" accepted.
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000NC"" started.
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000ND"" accepted.
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000ND"" started.
06/08/2025 15:18:17 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/swagger-ui-standalone-preset.js""" - null null
06/08/2025 15:18:17 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/swagger-ui-bundle.js""" - null null
06/08/2025 15:18:17 [DBG] The request path "/swagger/swagger-ui-standalone-preset.js" does not match an existing file
06/08/2025 15:18:17 [DBG] The request path "/swagger/swagger-ui-bundle.js" does not match an existing file
06/08/2025 15:18:17 [DBG] No candidates found for the request path '"/swagger/swagger-ui-bundle.js"'
06/08/2025 15:18:17 [DBG] No candidates found for the request path '"/swagger/swagger-ui-standalone-preset.js"'
06/08/2025 15:18:17 [DBG] Request did not match any endpoints
06/08/2025 15:18:17 [DBG] Request did not match any endpoints
06/08/2025 15:18:17 [INF] Sending file. Request path: '"/swagger-ui-standalone-preset.js"'. Physical path: '"N/A"'
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000NC"" completed keep alive response.
06/08/2025 15:18:17 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/swagger-ui-standalone-preset.js""" - 200 229224 "text/javascript" 92.5075ms
06/08/2025 15:18:17 [INF] Sending file. Request path: '"/swagger-ui-bundle.js"'. Physical path: '"N/A"'
06/08/2025 15:18:17 [DBG] Connection id ""0HNEKO76000ND"" completed keep alive response.
06/08/2025 15:18:17 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/swagger-ui-bundle.js""" - 200 1468327 "text/javascript" 112.0887ms
06/08/2025 15:18:18 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/v1/swagger.json""" - null null
06/08/2025 15:18:18 [DBG] The request path "/swagger/v1/swagger.json" does not match an existing file
06/08/2025 15:18:18 [DBG] No candidates found for the request path '"/swagger/v1/swagger.json"'
06/08/2025 15:18:18 [DBG] Request did not match any endpoints
06/08/2025 15:18:20 [DBG] Connection id ""0HNEKO76000NB"" completed keep alive response.
06/08/2025 15:18:20 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/v1/swagger.json""" - 200 null "application/json; charset=utf-8" 1804.8791ms
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000ND"" received FIN.
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000NC"" received FIN.
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000NB"" received FIN.
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000NB"" disconnecting.
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000NC"" disconnecting.
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000ND"" disconnecting.
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000NB"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000NC"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000ND"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000NC"" stopped.
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000ND"" stopped.
06/08/2025 15:23:38 [DBG] Connection id ""0HNEKO76000NB"" stopped.
06/08/2025 15:32:11 [DBG] Connection id ""0HNEKO76000NE"" accepted.
06/08/2025 15:32:11 [DBG] Connection id ""0HNEKO76000NE"" started.
06/08/2025 15:32:11 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/oauth2-redirect.html""" - null null
06/08/2025 15:32:11 [DBG] The request path "/swagger/oauth2-redirect.html" does not match an existing file
06/08/2025 15:32:11 [DBG] No candidates found for the request path '"/swagger/oauth2-redirect.html"'
06/08/2025 15:32:11 [DBG] Request did not match any endpoints
06/08/2025 15:32:11 [INF] The file "/oauth2-redirect.html" was not modified
06/08/2025 15:32:11 [DBG] Handled. Status code: 304 File: "/oauth2-redirect.html"
06/08/2025 15:32:11 [DBG] Connection id ""0HNEKO76000NE"" completed keep alive response.
06/08/2025 15:32:11 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/swagger/oauth2-redirect.html""" - 304 null "text/html" 5.2273ms
06/08/2025 15:32:21 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/api/v1/Organization/GetPage""" - null null
06/08/2025 15:32:21 [DBG] The request path "/api/v1/Organization/GetPage" does not match a supported file type
06/08/2025 15:32:21 [DBG] 1 candidate(s) found for the request path '"/api/v1/Organization/GetPage"'
06/08/2025 15:32:21 [DBG] Endpoint '"UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)"' with route pattern '"api/v1/Organization/GetPage"' is valid for the request path '"/api/v1/Organization/GetPage"'
06/08/2025 15:32:21 [DBG] Request matched endpoint '"UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)"'
06/08/2025 15:32:21 [DBG] Static files was skipped as the request already matched an endpoint.
06/08/2025 15:32:21 [DBG] Successfully validated the token.
06/08/2025 15:32:21 [DBG] AuthenticationScheme: "Bearer" was successfully authenticated.
06/08/2025 15:32:21 [INF] Executing endpoint '"UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)"'
06/08/2025 15:32:21 [INF] Route matched with "{action = \"GetPage\", controller = \"Organization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetPage(UNI.Master.Model.Organizations.FilterOrganization)" on controller "UNI.Master.API.Controllers.Version1.OrganizationController" ("UNI.Master.API").
06/08/2025 15:32:21 [DBG] Execution plan of "authorization" filters (in the following order): ["None"]
06/08/2025 15:32:21 [DBG] Execution plan of "resource" filters (in the following order): ["None"]
06/08/2025 15:32:21 [DBG] Execution plan of "action" filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)", "Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)", "UNI.Master.API.Attributes.AuthorizeAttribute (Order: 0)"]
06/08/2025 15:32:21 [DBG] Execution plan of "exception" filters (in the following order): ["None"]
06/08/2025 15:32:21 [DBG] Execution plan of "result" filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)", "UNI.Master.API.Attributes.AuthorizeAttribute (Order: 0)"]
06/08/2025 15:32:21 [DBG] Executing controller factory for controller "UNI.Master.API.Controllers.Version1.OrganizationController" ("UNI.Master.API")
06/08/2025 15:32:21 [DBG] Executed controller factory for controller "UNI.Master.API.Controllers.Version1.OrganizationController" ("UNI.Master.API")
06/08/2025 15:32:21 [DBG] Attempting to bind parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"' ...
06/08/2025 15:32:21 [DBG] Attempting to bind parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"' using the name '""' in request data ...
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."CustomerId"' of type '"System.Nullable`1[System.Guid]"' using the name '"CustomerId"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"CustomerId"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."CustomerId"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."CustomerId"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."id"' of type '"System.Nullable`1[System.Guid]"' using the name '"id"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"id"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."id"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."id"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."userId"' of type '"System.String"' using the name '"userId"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"userId"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."userId"' of type '"System.String"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."userId"' of type '"System.String"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."clientId"' of type '"System.String"' using the name '"clientId"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"clientId"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."clientId"' of type '"System.String"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."clientId"' of type '"System.String"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."acceptLanguage"' of type '"System.String"' using the name '"acceptLanguage"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"acceptLanguage"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."acceptLanguage"' of type '"System.String"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."acceptLanguage"' of type '"System.String"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."filter"' of type '"System.String"' using the name '"filter"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"filter"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."filter"' of type '"System.String"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."filter"' of type '"System.String"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."gridWidth"' of type '"System.Nullable`1[System.Int32]"' using the name '"gridWidth"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"gridWidth"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."gridWidth"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."gridWidth"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."customOid"' of type '"System.Nullable`1[System.Guid]"' using the name '"customOid"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"customOid"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."customOid"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."customOid"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."offSet"' of type '"System.Nullable`1[System.Int32]"' using the name '"offSet"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"offSet"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."offSet"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."offSet"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:32:21 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."pageSize"' of type '"System.Nullable`1[System.Int32]"' using the name '"pageSize"' in request data ...
06/08/2025 15:32:21 [DBG] Could not find a value in the request with name '"pageSize"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."pageSize"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."pageSize"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"'.
06/08/2025 15:32:21 [DBG] Done attempting to bind parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"'.
06/08/2025 15:32:21 [DBG] Attempting to validate the bound parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"' ...
06/08/2025 15:32:21 [DBG] Done attempting to validate the bound parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"'.
06/08/2025 15:32:22 [INF] Executed action "UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)" in 574.1495ms
06/08/2025 15:32:22 [INF] Executed endpoint '"UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)"'
06/08/2025 15:32:22 [ERR] HTTP "GET" "/api/v1/Organization/GetPage" responded 500 in 1201.6499 ms
Microsoft.Data.SqlClient.SqlException (0x80131904): @CustomerId is not a parameter for procedure sp_organization_page.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1042
   at UNI.Common.CommonBase.UniBaseRepository.GetMultipleAsync(String storedProcedure, DynamicParameters dynamicParam, Object objParams, ParametersHandler parametersHandler, UniReaderHandler readerHandler, Nullable`1 commandTimeout) in D:\Workspace\Unicloud\Backend\uni-master\uni-common\UNI.Common\CommonBase\UniBaseRepository.cs:line 1123
   at UNI.Common.CommonBase.UniBaseRepository.GetMultipleAsync(String storedProcedure, DynamicParameters dynamicParam, Object objParams, ParametersHandler parametersHandler, UniReaderHandler readerHandler, Nullable`1 commandTimeout) in D:\Workspace\Unicloud\Backend\uni-master\uni-common\UNI.Common\CommonBase\UniBaseRepository.cs:line 1124
   at UNI.Common.CommonBase.UniBaseRepository.GetPageAsync[TV,T](String storedProcedure, FilterInput filter, Object objParams, ParametersHandler parametersHandler, UniReaderHandler`1 readerHandler) in D:\Workspace\Unicloud\Backend\uni-master\uni-common\UNI.Common\CommonBase\UniBaseRepository.cs:line 251
   at UNI.Master.API.Controllers.Version1.OrganizationController.GetPage(FilterOrganization query) in D:\Workspace\Unicloud\Backend\uni-master\UNI.Master.API\Controllers\Version1\OrganizationController.cs:line 38
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
ClientConnectionId:9aa9966d-4e9c-4bb8-bc61-19b1b8c23999
Error Number:8145,State:1,Class:16
06/08/2025 15:32:22 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.Data.SqlClient.SqlException (0x80131904): @CustomerId is not a parameter for procedure sp_organization_page.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Dapper.SqlMapper.QueryMultipleAsync(IDbConnection cnn, CommandDefinition command) in /_/Dapper/SqlMapper.Async.cs:line 1042
   at UNI.Common.CommonBase.UniBaseRepository.GetMultipleAsync(String storedProcedure, DynamicParameters dynamicParam, Object objParams, ParametersHandler parametersHandler, UniReaderHandler readerHandler, Nullable`1 commandTimeout) in D:\Workspace\Unicloud\Backend\uni-master\uni-common\UNI.Common\CommonBase\UniBaseRepository.cs:line 1123
   at UNI.Common.CommonBase.UniBaseRepository.GetMultipleAsync(String storedProcedure, DynamicParameters dynamicParam, Object objParams, ParametersHandler parametersHandler, UniReaderHandler readerHandler, Nullable`1 commandTimeout) in D:\Workspace\Unicloud\Backend\uni-master\uni-common\UNI.Common\CommonBase\UniBaseRepository.cs:line 1124
   at UNI.Common.CommonBase.UniBaseRepository.GetPageAsync[TV,T](String storedProcedure, FilterInput filter, Object objParams, ParametersHandler parametersHandler, UniReaderHandler`1 readerHandler) in D:\Workspace\Unicloud\Backend\uni-master\uni-common\UNI.Common\CommonBase\UniBaseRepository.cs:line 251
   at UNI.Master.API.Controllers.Version1.OrganizationController.GetPage(FilterOrganization query) in D:\Workspace\Unicloud\Backend\uni-master\UNI.Master.API\Controllers\Version1\OrganizationController.cs:line 38
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.TaskOfIActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Serilog.AspNetCore.RequestLoggingMiddleware.Invoke(HttpContext httpContext)
   at NSwag.AspNetCore.Middlewares.SwaggerUiIndexMiddleware.Invoke(HttpContext context)
   at NSwag.AspNetCore.Middlewares.RedirectToIndexMiddleware.Invoke(HttpContext context)
   at NSwag.AspNetCore.Middlewares.OpenApiDocumentMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
ClientConnectionId:9aa9966d-4e9c-4bb8-bc61-19b1b8c23999
Error Number:8145,State:1,Class:16
06/08/2025 15:32:22 [DBG] Connection id ""0HNEKO76000NE"" completed keep alive response.
06/08/2025 15:32:22 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/api/v1/Organization/GetPage""" - 500 null "text/plain; charset=utf-8" 1473.5441ms
06/08/2025 15:38:21 [DBG] Connection id ""0HNEKO76000NE"" received FIN.
06/08/2025 15:38:21 [DBG] Connection id ""0HNEKO76000NE"" disconnecting.
06/08/2025 15:38:21 [DBG] Connection id ""0HNEKO76000NE"" stopped.
06/08/2025 15:38:21 [DBG] Connection id ""0HNEKO76000NE"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
06/08/2025 15:42:45 [DBG] Connection id ""0HNEKO76000NF"" accepted.
06/08/2025 15:42:45 [DBG] Connection id ""0HNEKO76000NF"" started.
06/08/2025 15:42:45 [INF] Request starting "HTTP/1.1" "GET" "http"://"localhost:3103""""/api/v1/Organization/GetPage""" - null null
06/08/2025 15:42:45 [DBG] The request path "/api/v1/Organization/GetPage" does not match a supported file type
06/08/2025 15:42:45 [DBG] 1 candidate(s) found for the request path '"/api/v1/Organization/GetPage"'
06/08/2025 15:42:45 [DBG] Endpoint '"UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)"' with route pattern '"api/v1/Organization/GetPage"' is valid for the request path '"/api/v1/Organization/GetPage"'
06/08/2025 15:42:45 [DBG] Request matched endpoint '"UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)"'
06/08/2025 15:42:45 [DBG] Static files was skipped as the request already matched an endpoint.
06/08/2025 15:42:45 [DBG] Successfully validated the token.
06/08/2025 15:42:45 [DBG] AuthenticationScheme: "Bearer" was successfully authenticated.
06/08/2025 15:42:45 [INF] Executing endpoint '"UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)"'
06/08/2025 15:42:45 [INF] Route matched with "{action = \"GetPage\", controller = \"Organization\"}". Executing controller action with signature "System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] GetPage(UNI.Master.Model.Organizations.FilterOrganization)" on controller "UNI.Master.API.Controllers.Version1.OrganizationController" ("UNI.Master.API").
06/08/2025 15:42:45 [DBG] Execution plan of "authorization" filters (in the following order): ["None"]
06/08/2025 15:42:45 [DBG] Execution plan of "resource" filters (in the following order): ["None"]
06/08/2025 15:42:45 [DBG] Execution plan of "action" filters (in the following order): ["Microsoft.AspNetCore.Mvc.ModelBinding.UnsupportedContentTypeFilter (Order: -3000)", "Microsoft.AspNetCore.Mvc.Infrastructure.ModelStateInvalidFilter (Order: -2000)", "UNI.Master.API.Attributes.AuthorizeAttribute (Order: 0)"]
06/08/2025 15:42:45 [DBG] Execution plan of "exception" filters (in the following order): ["None"]
06/08/2025 15:42:45 [DBG] Execution plan of "result" filters (in the following order): ["Microsoft.AspNetCore.Mvc.Infrastructure.ClientErrorResultFilter (Order: -2000)", "UNI.Master.API.Attributes.AuthorizeAttribute (Order: 0)"]
06/08/2025 15:42:45 [DBG] Executing controller factory for controller "UNI.Master.API.Controllers.Version1.OrganizationController" ("UNI.Master.API")
06/08/2025 15:42:45 [DBG] Executed controller factory for controller "UNI.Master.API.Controllers.Version1.OrganizationController" ("UNI.Master.API")
06/08/2025 15:42:45 [DBG] Attempting to bind parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"' ...
06/08/2025 15:42:45 [DBG] Attempting to bind parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"' using the name '""' in request data ...
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."CustomerId"' of type '"System.Nullable`1[System.Guid]"' using the name '"CustomerId"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"CustomerId"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."CustomerId"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."CustomerId"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."id"' of type '"System.Nullable`1[System.Guid]"' using the name '"id"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"id"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."id"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."id"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."userId"' of type '"System.String"' using the name '"userId"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"userId"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."userId"' of type '"System.String"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."userId"' of type '"System.String"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."clientId"' of type '"System.String"' using the name '"clientId"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"clientId"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."clientId"' of type '"System.String"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."clientId"' of type '"System.String"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."acceptLanguage"' of type '"System.String"' using the name '"acceptLanguage"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"acceptLanguage"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."acceptLanguage"' of type '"System.String"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."acceptLanguage"' of type '"System.String"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."filter"' of type '"System.String"' using the name '"filter"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"filter"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."filter"' of type '"System.String"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."filter"' of type '"System.String"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."gridWidth"' of type '"System.Nullable`1[System.Int32]"' using the name '"gridWidth"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"gridWidth"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."gridWidth"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."gridWidth"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."customOid"' of type '"System.Nullable`1[System.Guid]"' using the name '"customOid"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"customOid"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."customOid"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."customOid"' of type '"System.Nullable`1[System.Guid]"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."offSet"' of type '"System.Nullable`1[System.Int32]"' using the name '"offSet"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"offSet"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."offSet"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."offSet"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:42:45 [DBG] Attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."pageSize"' of type '"System.Nullable`1[System.Int32]"' using the name '"pageSize"' in request data ...
06/08/2025 15:42:45 [DBG] Could not find a value in the request with name '"pageSize"' for binding property '"UNI.Master.Model.Organizations.FilterOrganization"."pageSize"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind property '"UNI.Master.Model.Organizations.FilterOrganization"."pageSize"' of type '"System.Nullable`1[System.Int32]"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"'.
06/08/2025 15:42:45 [DBG] Done attempting to bind parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"'.
06/08/2025 15:42:45 [DBG] Attempting to validate the bound parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"' ...
06/08/2025 15:42:45 [DBG] Done attempting to validate the bound parameter '"query"' of type '"UNI.Master.Model.Organizations.FilterOrganization"'.
06/08/2025 15:42:45 [DBG] List of registered output formatters, in the following order: ["Microsoft.AspNetCore.Mvc.Formatters.HttpNoContentOutputFormatter", "Microsoft.AspNetCore.Mvc.Formatters.StringOutputFormatter", "Microsoft.AspNetCore.Mvc.Formatters.StreamOutputFormatter", "Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter"]
06/08/2025 15:42:45 [DBG] Attempting to select an output formatter based on Accept header '["application/octet-stream"]'.
06/08/2025 15:42:45 [DBG] Could not find an output formatter based on content negotiation. Accepted types were (["application/octet-stream"])
06/08/2025 15:42:45 [DBG] Attempting to select an output formatter without using a content type as no explicit content types were specified for the response.
06/08/2025 15:42:45 [DBG] Attempting to select the first formatter in the output formatters list which can write the result.
06/08/2025 15:42:45 [DBG] Selected output formatter '"Microsoft.AspNetCore.Mvc.Formatters.NewtonsoftJsonOutputFormatter"' and content type '"application/json"' to write the response.
06/08/2025 15:42:45 [INF] Executing "OkObjectResult", writing value of type '"UNI.Model.BaseResponse`1[[UNI.Model.CommonListPage, UNI.Model, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]"'.
06/08/2025 15:42:45 [INF] Executed action "UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)" in 156.2094ms
06/08/2025 15:42:45 [INF] Executed endpoint '"UNI.Master.API.Controllers.Version1.OrganizationController.GetPage (UNI.Master.API)"'
06/08/2025 15:42:45 [INF] HTTP "GET" "/api/v1/Organization/GetPage" responded 200 in 171.9383 ms
06/08/2025 15:42:45 [DBG] Connection id ""0HNEKO76000NF"" completed keep alive response.
06/08/2025 15:42:45 [INF] Request finished "HTTP/1.1" "GET" "http"://"localhost:3103""""/api/v1/Organization/GetPage""" - 200 272 "application/json; charset=utf-8" 177.5021ms
06/08/2025 15:47:50 [DBG] Connection id ""0HNEKO76000NF"" received FIN.
06/08/2025 15:47:50 [DBG] Connection id ""0HNEKO76000NF"" disconnecting.
06/08/2025 15:47:50 [DBG] Connection id ""0HNEKO76000NF"" stopped.
06/08/2025 15:47:50 [DBG] Connection id ""0HNEKO76000NF"" sending FIN because: ""The Socket transport's send loop completed gracefully.""
06/08/2025 19:28:50 [INF] Application Starting.
