﻿using UNI.Master.Model.UniMaster;
using UNI.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.DAL.Interfaces;

namespace UNI.Master.BLL.BusinessService
{
    public class ProductFeatureService: IProductFeatureService
    {
        private readonly IProductFeatureRepository _productFeatureRepository;
        public ProductFeatureService(IProductFeatureRepository productFeatureRepository)
        {
            if (productFeatureRepository != null)
                _productFeatureRepository = productFeatureRepository;
        }

        public Task<BaseValidate> DelProductFeatureInfo(Guid id)
        {
            return _productFeatureRepository.DelProductFeatureInfo(id);
        }

        public Task<productFeatureInfo> GetProductFeatureInfo(Guid? id, Guid? productModuleId)
        {
            return _productFeatureRepository.GetProductFeatureInfo(id, productModuleId);
        }

        public Task<CommonListPage> GetProductFeaturePage(FilterProductFeature flt)
        {
            return _productFeatureRepository.GetProductFeaturePage(flt);
        }

        public Task<BaseValidate> SetProductFeatureInfo(productFeatureInfo prod)
        {
            return _productFeatureRepository.SetProductFeatureInfo(prod);
        }
        public Task<List<CommonValue>> GetProductFeatureList(string filter, string productModuleId)
        {
            return _productFeatureRepository.GetProductFeatureList(filter, productModuleId);
        }
    }
}
