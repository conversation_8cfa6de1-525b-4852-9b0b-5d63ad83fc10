using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Deployments;
using UNI.Master.Model.Deployments;
using UNI.Model;

namespace UNI.Master.DAL.Repositories.Deployments
{
    /// <summary>
    /// Deployment Database Repository Implementation
    /// </summary>
    public class DeploymentDatabaseRepository : UniBaseRepository, IDeploymentDatabaseRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="common">Common repository base</param>
        public DeploymentDatabaseRepository(IUniCommonBaseRepository common) : base(common)
        {
        }

        /// <summary>
        /// Get deployment databases page
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of deployment databases</returns>
        public Task<CommonListPage> GetPageAsync(DeploymentDatabaseFilter filter)
        {
            return base.GetPageAsync("sp_deployment_database_page", filter, new 
            { 
                filter.CustomerId, 
                filter.ProductId, 
                filter.DeploymentId, 
                filter.Status,
                filter.CreatedFrom,
                filter.CreatedTo
            });
        }

        /// <summary>
        /// Get deployment database info by ID
        /// </summary>
        /// <param name="id">Database ID</param>
        /// <returns>Database information</returns>
        public Task<DeploymentDatabaseInfo> GetInfoAsync(Guid? id)
        {
            return base.GetFieldsAsync<DeploymentDatabaseInfo>("sp_deployment_database_fields", new { id });
        }

        /// <summary>
        /// Create or update deployment database
        /// </summary>
        /// <param name="info">Database information</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> SetInfoAsync(DeploymentDatabaseInfo info)
        {
            return base.SetInfoAsync("sp_deployment_database_set", info, new { info.Id });
        }

        /// <summary>
        /// Delete deployment database
        /// </summary>
        /// <param name="id">Database ID</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> DeleteAsync(Guid? id)
        {
            return base.DeleteAsync("sp_deployment_database_del", new { id });
        }

        /// <summary>
        /// Create deployment database for customer and product
        /// </summary>
        /// <param name="request">Create request</param>
        /// <returns>Validation result with database ID</returns>
        public Task<BaseValidate<Guid?>> CreateDeploymentDatabaseAsync(CreateDeploymentDatabaseRequest request)
        {
            return base.SetAsync<Guid?>("sp_deployment_database_create", request);
        }

        /// <summary>
        /// Update deployment database
        /// </summary>
        /// <param name="request">Update request</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> UpdateDeploymentDatabaseAsync(UpdateDeploymentDatabaseRequest request)
        {
            return base.SetAsync("sp_deployment_database_update", request);
        }

        /// <summary>
        /// Get deployment databases by customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <param name="productId">Optional product ID filter</param>
        /// <returns>List of deployment databases</returns>
        public async Task<IEnumerable<DeploymentDatabaseDto>> GetByCustomerAsync(Guid customerId, Guid? productId = null)
        {
            return await base.GetAsync<DeploymentDatabaseDto>("sp_deployment_database_by_customer", 
                new { customerId, productId });
        }

        /// <summary>
        /// Get deployment databases by deployment
        /// </summary>
        /// <param name="deploymentId">Deployment ID</param>
        /// <returns>List of deployment databases</returns>
        public async Task<IEnumerable<DeploymentDatabaseDto>> GetByDeploymentAsync(Guid deploymentId)
        {
            return await base.GetAsync<DeploymentDatabaseDto>("sp_deployment_database_by_deployment", 
                new { deploymentId });
        }

        /// <summary>
        /// Initialize database schema
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <param name="versionId">Schema version ID</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> InitializeDatabaseSchemaAsync(Guid databaseId, Guid versionId)
        {
            return base.SetAsync("sp_deployment_database_schema_init", new { databaseId, versionId });
        }

        /// <summary>
        /// Migrate database schema
        /// </summary>
        /// <param name="request">Migration request</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> MigrateDatabaseSchemaAsync(DatabaseSchemaMigrationRequest request)
        {
            return base.SetAsync("sp_deployment_database_schema_migrate", request);
        }

        /// <summary>
        /// Check database connection
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> CheckConnectionAsync(Guid databaseId)
        {
            return base.SetAsync("sp_deployment_database_check_connection", new { databaseId });
        }

        /// <summary>
        /// Update database status
        /// </summary>
        /// <param name="databaseId">Database ID</param>
        /// <param name="status">New status</param>
        /// <returns>Validation result</returns>
        public Task<BaseValidate> UpdateStatusAsync(Guid databaseId, string status)
        {
            return base.SetAsync("sp_deployment_database_update_status", new { databaseId, status });
        }

        /// <summary>
        /// Get database statistics
        /// </summary>
        /// <param name="customerId">Optional customer ID filter</param>
        /// <param name="productId">Optional product ID filter</param>
        /// <returns>Database statistics</returns>
        public async Task<object> GetStatisticsAsync(Guid? customerId = null, Guid? productId = null)
        {
            return await base.GetFirstOrDefaultAsync<object>("sp_deployment_database_statistics", 
                new { customerId, productId });
        }
    }
}
