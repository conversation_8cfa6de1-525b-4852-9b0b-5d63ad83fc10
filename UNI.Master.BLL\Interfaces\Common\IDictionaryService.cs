﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.BLL.Interfaces.Common
{
    /// <summary>
    /// 
    /// </summary>
    public interface IDictionaryService
    {

        #region dic-reg
        Task<CommonViewInfo> GetDicFilter();
        Task<List<DicItem>> GetDicTrees(string group_code, string filter);
        Task<List<DicItemTree>> GetDicTreeNode(string group_code, string filter);
        Task<CommonListPage> GetDicPage(FilterBase flt, string table);
        Task<DicInfo> GetDicInfo(string tableName, Guid? id);
        Task<BaseValidate> SetDicInfo(DicInfo info);
        Task<BaseValidate> DelDicInfo(string tableName, Guid id);
        Task<BaseValidate> DelMultiDicInfo(CommonDeleteMulti commonDeleteMulti);

        #endregion cdic-reg

        #region import
        Task<ImportListPage> SetDicImport(List<DicFieldItem> records, string tableName, bool accept);
        Task<ImportListPage> SetDicImport(string tableName, DicImportSetV1 records);
        Task<BaseValidate<Stream>> SetDicExport(string tableName, bool includeData, string filter);
        Task<BaseValidate<Stream>> SetDicExportDraft(DicImportSet importSet);
        Task<BaseValidate<Stream>> GetDicImportTemp();


        #endregion
    }
}
