﻿using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Model;

namespace UNI.Master.DAL.Repositories.Common
{
    /// <summary>
    /// Worktime Repository
    /// </summary>
    /// Author: 
    /// CreatedDate: 16/11/2016 2:07 PM
    /// <seealso cref="IMetaImportRepository" />
    public class MetaImportRepository : UniBaseRepository, IMetaImportRepository
    {

        public MetaImportRepository(IUniCommonBaseRepository commonInfo) : base(commonInfo)
        {
        }
        public async Task<CommonListPage> GetImportPage(FilterInput flt, string import_type)
        {
            const string storedProcedure = "sp_meta_import_page";
            return await GetPageAsync(storedProcedure, flt, new { import_type });
        }
        public async Task<BaseValidate> DelImport(Guid impId)
        {
            const string storedProcedure = "sp_meta_import_del";
            return await base.DeleteAsync(storedProcedure, new { impId });
        }

    }
}

