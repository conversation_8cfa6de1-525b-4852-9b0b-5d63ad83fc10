﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.BusinessService.Common;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Model;
using UNI.Model.Api;
using UNI.Model.Core;

namespace UNI.Master.API.Controllers.Version1.Common
{
    /// <summary>
    /// Commons api
    /// </summary>
    [Route("api/v1/common/[action]")]
    //[Authorize(AuthenticationSchemes = IdentityServerAuthenticationDefaults.AuthenticationScheme)]
    public class CommonController : UniController
    {
        private readonly ICommonService _service;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="service"></param>
        public CommonController(ILoggerFactory logger,
            ICommonService service) : base(logger)
        {
            if (service != null)
                _service = service;
        }
        /// <summary>
        /// GetObjectGroup
        /// </summary>
        /// <param name="objKey">idcard_type_group: loai gi<PERSON>y tờ</param>
        /// <param name="all"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetObjects([FromQuery] string objKey, [FromQuery] string all, [FromQuery] string filter)
        {
            var result = await _service.GetObjects(objKey, all, filter);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// GetObjectClass
        /// </summary>
        /// <param name="objKey"></param>
        /// <param name="all"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetObjectClass([FromQuery] string objKey, [FromQuery] string all, [FromQuery] string filter)
        {
            var result = await _service.GetObjectClass(objKey, all);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// Get GetCountries - Quốc gia
        /// </summary>
        /// <param name="resCode"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetCountries(string resCode, string filter)
        {
            var result = await _service.GetCountries(filter, resCode);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// GetCommonslist - Danh sách : Danh mục chung từ bất kì bảng nào
        /// </summary>
        /// <param name="common"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetCommonList(
             [FromQuery] CommonInput common
            )
        {
            var result = await _service.GetCommonList(common);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// Get Month List
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetMonthList()
        {
            var result = await _service.GetMonthList();
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// Lấy danh sách năm
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetYearList()
        {
            var result = await _service.GetYearList();
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// GetAddressDetail - Chi tiết : Địa chỉ
        /// </summary>
        /// <param name="Oid"></param>       
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewInfo>> GetAddressDetail([FromQuery] Guid? Oid)
        {
            try
            {
                var result = await _service.GetAddressDetail(Oid);
                return GetResponse(ApiResult.Success, result);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "{Message}", e.Message);
                var rp = new BaseResponse<CommonViewInfo>(ApiResult.Error, e.Message);
                return rp;
            }
        }
        /// <summary>
        /// GetAddressDetailDraft - Chi tiết : Địa chỉ thuế
        /// </summary>
        /// <param name="info"></param>       
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<CommonViewInfo>> GetAddressDetailDraft([FromBody] CommonViewInfo info)
        {

            var result = await _service.GetAddressDetailDraft(info);
            return GetResponse(ApiResult.Success, result);

        }
        ///// <summary>
        ///// GetAddressTax - Chi tiết : Địa chỉ thuế
        ///// </summary>
        ///// <param name="tax"></param>       
        ///// <returns></returns>
        //[HttpGet]
        //public async Task<BaseResponse<CommonViewInfo>> GetAddressTax([FromQuery] AddressTax tax)
        //{

        //    var result = await _service.GetAddressTax(tax);
        //    return GetResponse(ApiResult.Success, result);

        //}
        /// <summary>
        /// GetAddressStreets- Lấy danh sách địa chỉ đã có
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetAddressStreets([FromQuery] AddressParam param)
        {
            var result = await _service.GetAddressStreets(param);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// GetOrganizeList - Danh sách tổ chức theo từng cấp
        /// </summary>
        /// <param name="Oid"></param>
        /// <param name="org_level">Cấp tổ chức</param>
        /// <param name="parentId">cấp trực thuộc</param>
        /// <param name="all"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetOrganizeList([FromQuery] Guid? Oid, [FromQuery] int org_level, [FromQuery] Guid? parentId, [FromQuery] string all, [FromQuery] string filter)
        {
            var result = await _service.GetOrganizeList(Oid, org_level, parentId, all, filter);
            return GetResponse(ApiResult.Success, result);
        }
    }
}
