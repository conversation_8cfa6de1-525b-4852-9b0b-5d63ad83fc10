﻿using System.Threading.Tasks;
using System;
using UNI.Master.Model.Products;
using UNI.Model;
using UNI.Master.Model.Filters;
using UNI.Master.Model.UniMaster;

namespace UNI.Master.DAL.Interfaces.Prodducts
{
    public interface IProductModuleRepository
    {
        Task<CommonListPage> GetPageAsync(FilterProductModule query);
        Task<ProductModuleInfo> GetInfoAsync(Guid? id, Guid? packageDetailId);
        Task<BaseValidate> SetInfoAsync(ProductModuleInfo info);
        Task<BaseValidate> DeleteAsync(Guid? id);
    }
}
