﻿using UNI.Master.Model.UniMaster;
using UNI.Model;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace UNI.Master.DAL.Interfaces
{
    public interface IProductFeatureRepository
    {
        Task<CommonListPage> GetProductFeaturePage(FilterProductFeature flt);
        Task<productFeatureInfo> GetProductFeatureInfo(Guid? id, Guid? productModuleId);
        Task<BaseValidate> SetProductFeatureInfo(productFeatureInfo info);
        Task<BaseValidate> DelProductFeatureInfo(Guid id);
        Task<List<CommonValue>> GetProductFeatureList(string filter, string productModuleId);
    }
}
