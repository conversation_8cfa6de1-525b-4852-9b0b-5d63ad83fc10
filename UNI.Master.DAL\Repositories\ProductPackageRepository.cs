﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.DAL.Repositories
{
    public class ProductPackageRepository : UniBaseRepository, IProductPackageRepository
    {
        public ProductPackageRepository(IUniCommonBaseRepository commonInfo) : base(commonInfo)
        {
        }

        public async Task<CommonListPage> GetProductPackagePage(FilterProdPackage flt)
        {
            const string storedProcedure = "sp_product_package_get";
            return await base.GetPageAsync(storedProcedure, flt, param =>
            {
                param.Add("@product_id", flt.productId);
                return param;
            });
        }

        public async Task<productPackageInfo> GetProductPackageInfo(Guid? id)
        {
            const string storedProcedure = "sp_product_package_fields";
            return await base.GetFieldsAsync<productPackageInfo>(storedProcedure, new { id });
        }

        public async Task<BaseValidate> SetProductPackageInfo(productPackageInfo query)
        {
            const string storedProcedure = "sp_product_package_set";
            return await base.SetInfoAsync<BaseValidate>(storedProcedure, query, new { query.id });
        }

        public async Task<BaseValidate> DelProductPackageInfo(Guid id)
        {
            const string storedProcedure = "sp_product_package_del";
            return await base.DeleteAsync(storedProcedure, new { id });
        }

        public async Task<List<CommonValue>> GetProductPackageList(string filter)
        {
            const string storedProcedure = "sp_product_package_list_get";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { filter });
        }

        public async Task<CommonListPage> GetProductByPackageId(Guid? product_package_id)
        {
            const string storedProcedure = "sp_product_by_package_id_get";
            return await base.GetPageAsync(storedProcedure, null, param =>
            {
                param.Add("@product_package_id", product_package_id);
                param.Add("@Offset", 0);
                param.Add("@PageSize", 100);
                return param;
            });
        }


        #region PackageDetail
        public async Task<ProductPackageDetailInfo> GetProductPackageDetailInfo(Guid? id, Guid? productId)
        {
            const string storedProcedure = "sp_product_package_details_fields";
            return await base.GetFieldsAsync<ProductPackageDetailInfo>(storedProcedure, new { id, productId });
        }

        public async Task<BaseValidate> SetProductPackageDetailInfo(ProductPackageDetailInfo data)
        {
            const string storedProcedure = "sp_product_package_details_set";
            return await base.SetInfoAsync<BaseValidate>(storedProcedure, data, new { data.id, data.productId });
        }

        public async Task<List<CommonValue>> GetProductPackageDetailList(string filter)
        {
            const string storedProcedure = "sp_product_package_details_list_get";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { filter });
        }

        public async Task<BaseValidate> DelProductPackageDetailInfo(Guid id)
        {
            const string storedProcedure = "sp_product_package_details_del";
            return await base.DeleteAsync(storedProcedure, new { id });
        }

        public async Task<CommonListPage> GetProductPackageDetailsPage(FilterProdPackage flt)
        {
            const string storedProcedure = "sp_product_package_details_get";
            return await base.GetPageAsync(storedProcedure, flt, param =>
            {
                param.Add("@product_id", flt.productId);
                return param;
            });
        }
        #endregion PackageDetail




        // Product package detail description
        public async Task<ProductPackageDetailDescriptionInfo> GetProductPackageDetailDescriptionInfo(ProductPackageDetailDescriptionGet data)
        {
            const string storedProcedure = "sp_product_package_detail_description_fields";
            return await base.GetFieldsAsync<ProductPackageDetailDescriptionInfo>(storedProcedure, new {
                id = data.id,
                package_detail_id = data.packageDetailId
            });
        }

        public async Task<BaseValidate> SetProductPackageDetailDescriptionInfo(ProductPackageDetailDescriptionInfo query)
        {
            const string storedProcedure = "sp_product_package_detail_description_set";
            return await base.SetInfoAsync<BaseValidate>(storedProcedure, query, new { query.id, query.packageDetailId });
        }

        public async Task<BaseValidate> DelProductPackageDetailDescriptionInfo(Guid id)
        {
            const string storedProcedure = "sp_product_package_detail_description_del";
            return await base.DeleteAsync(storedProcedure, new { id });
        }

        public async Task<CommonListPage> GetProductPackageDetailDescriptionList(FilterProductPackageDetailDescription flt)
        {
            const string storedProcedure = "sp_product_package_detail_description_page";
            return await base.GetPageAsync(storedProcedure, flt,
            param =>
            {
                param.Add("@package_detail_id", flt.packageDetailId);
                return param;
            });
        }
    }
}
