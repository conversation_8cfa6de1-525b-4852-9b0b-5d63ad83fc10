﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Api;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Model;

namespace UNI.Master.BLL.BusinessService.Common
{
    /// <summary>
    /// IProjectService
    /// <author>hoanpv</author>
    /// <date>2024/09/30</date>
    /// </summary>
    public class MetaService : IMetaService
    {
        private readonly IMetaRepository _metaRepository;
        private readonly IApiStorageService _storageService;
        public MetaService(IMetaRepository FileRepository,
            IApiStorageService storageService
            )
        {
            _metaRepository = FileRepository;
            _storageService = storageService;
        }
        public Task<CommonViewInfo> GetMetaFilter()
        {
            return _metaRepository.GetMetaFilter();
        }
        public async Task<CommonListPage> GetMetaPage(FilterBase filter, string source_type, int? meta_type, string baseUrl)
        {
            return await _metaRepository.GetMetaPage(filter, source_type, meta_type, baseUrl);
        }
        public async Task<BaseValidate> SetMetaInfo(CommonViewOidInfo File)
        {
            return await _metaRepository.SetMetaInfo(File);
        }
        public async Task<CommonViewOidInfo> GetMetaInfo(Guid? Oid, Guid? parentOid, string source_type)
        {
            return await _metaRepository.GetMetaInfo(Oid, parentOid, source_type);
        }
        public async Task<BaseValidate> DelMetaInfo(string Oids)
        {
            return await _metaRepository.DelMetaInfo(Oids);
        }
        public async Task<List<MediaItem>> GetMetaTrees(string source_type, Guid? parentOid, string filter)
        {
            return await _metaRepository.GetMetaTrees(source_type, parentOid, filter);
        }
        public Task<List<MediaItemTree>> GetMetaTreeNode(string source_type, Guid? parentOid, string filter)
        {
            return _metaRepository.GetMetaTreeNode(source_type, parentOid, filter);
        }
        public async Task<BaseValidate> SetMetaUpload(MediaFile info)
        {
            var file1 = await _storageService.UploadFile(info.formFile, info.source_type);
            return await _metaRepository.SetMetaUpload(info, file1);
        }
        public async Task<List<CommonValue>> GetFileList(string source_type, int meta_type, Guid sourceOid)
        {
            return await _metaRepository.GetFileList(source_type, meta_type, sourceOid);
        }
        public async Task<List<FileStorageInfo>> GetMetaDetail(Guid? oid, Guid? parentOid)
        {
            return await _metaRepository.GetMetaDetail(oid, parentOid);
        }
        
    }
}
