﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Model;

namespace UNI.Master.BLL.Interfaces.Common
{
    /// <summary>
    /// IFileService
    /// <author>hoanpv</author>
    /// <date>2024-09-30</date>
    /// </summary>
    public interface IMetaService
    {
        Task<CommonViewInfo> GetMetaFilter();
        Task<CommonListPage> GetMetaPage(FilterBase filter, string source_type, int? meta_type1, string baseUrl);
        Task<BaseValidate> SetMetaInfo(CommonViewOidInfo file);
        Task<CommonViewOidInfo> GetMetaInfo(Guid? Oid, Guid? parentOid, string source_type);
        Task<BaseValidate> DelMetaInfo(string oid);
        Task<List<MediaItem>> GetMetaTrees(string source_type, Guid? parentOid, string filter);
        Task<List<MediaItemTree>> GetMetaTreeNode(string source_type, Guid? parentOid, string filter);
        Task<BaseValidate> SetMetaUpload(MediaFile info);
        Task<List<CommonValue>> GetFileList(string source_type, int meta_type, Guid sourceOid);
        Task<List<FileStorageInfo>> GetMetaDetail(Guid? oid, Guid? parentOid);

    }
}
