﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Model;
using UNI.Model.Commons;
using UNI.Utils;

namespace UNI.Master.BLL.BusinessService.Common
{
    /// <summary>
    /// Class Dictionary Service.
    /// <author>TH</author>
    /// <date>2015/12/02</date>
    /// </summary>
    public class DictionaryService : IDictionaryService
    {
        private readonly IDictionaryRepository _dicRepository;
        private readonly IMetaService _metaService;
        public DictionaryService(IDictionaryRepository comRepository, IMetaService metaService)
        {
            _dicRepository = comRepository;
            _metaService = metaService;
        }
        #region dic-reg
        public Task<CommonViewInfo> GetDicFilter()
        {
            return _dicRepository.GetDicFilter();
        }
        public Task<List<DicItem>> GetDicTrees(string group_code, string filter)
        {
            return _dicRepository.GetDicTrees(group_code, filter);
        }
        public Task<List<DicItemTree>> GetDicTreeNode(string group_code, string filter)
        {
            return _dicRepository.GetDicTreeNode(group_code, filter);
        }
        public async Task<CommonListPage> GetDicPage(FilterBase flt, string table)
        {
            return await _dicRepository.GetDicPage(flt, table);
        }
        public Task<DicInfo> GetDicInfo(string tableName, Guid? Oid)
        {
            return _dicRepository.GetDicInfo(tableName, Oid);
        }
        public Task<BaseValidate> SetDicInfo(DicInfo info)
        {
            return _dicRepository.SetDicInfo(info);
        }
        public Task<BaseValidate> DelDicInfo(string tableName, Guid id)
        {
            return _dicRepository.DelDicInfo(tableName, id);
        }
        public Task<BaseValidate> DelMultiDicInfo(CommonDeleteMulti commonDeleteMulti)
        {
            return _dicRepository.DelMultiDicInfo(commonDeleteMulti);
        }

        #endregion dic-reg

        #region import
        public Task<ImportListPage> SetDicImport(List<DicFieldItem> records, string tableName, bool accept)
        {
            return _dicRepository.SetDicImport(records, tableName, accept);
        }
        public Task<ImportListPage> SetDicImport(string tableName, DicImportSetV1 records)
        {
            return _dicRepository.SetDicImport(tableName, records);
        }
        public async Task<BaseValidate<Stream>> SetDicExport(string tableName, bool includeData, string filter)
        {
            
            var ds = await _dicRepository.SetDicExport(tableName, includeData, filter);
            var r = new FlexcellUtils();
            var template = await File.ReadAllBytesAsync($"templates/exports/commonlists/export_danh_muc_chung.xlsx");
            Dictionary<string, object> p = new Dictionary<string, object>();
            var report = r.CreateReport(template, ReportType.xlsx, ds, p);
            return new BaseValidate<Stream>(report);
            
        }
        public async Task<BaseValidate<Stream>> SetDicExportDraft(DicImportSet importSet)
        {
            var ds = new DataSet();
            var r = new FlexcellUtils();
            var tb = r.ToDataTable(importSet.imports);
            ds.Tables.Add(tb);
            var template = await File.ReadAllBytesAsync($"templates/exports/commonlists/export_danh_muc_chung.xlsx");
            Dictionary<string, object> p = new Dictionary<string, object>();
            var report = r.CreateReport(template, ReportType.xlsx, ds, p);
            return new BaseValidate<Stream>(report);
        }
        public async Task<BaseValidate<Stream>> GetDicImportTemp()
        {
            try
            {
                var ds = await _dicRepository.GetDicImportTemp();
                var r = new FlexcellUtils();
                var template = await File.ReadAllBytesAsync($"templates/imports/commonlists/export_danh_muc_chung.xlsx");
                Dictionary<string, object> p = new Dictionary<string, object>();
                var report = r.CreateReport(template, ReportType.xlsx, ds, p);
                return new BaseValidate<Stream>(report);
            }
            catch (Exception ex)
            {
                return new BaseValidate<Stream>(null);
            }
        }

        #endregion
    }
}
