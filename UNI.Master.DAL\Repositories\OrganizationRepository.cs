using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces;
using UNI.Master.Model.Organizations;
using UNI.Master.Model.Common;
using UNI.Model;

namespace UNI.Master.DAL.Repositories;

public class OrganizationRepository : UniBaseRepository, IOrganizationRepository
{


    public OrganizationRepository(IUniCommonBaseRepository common) : base(common)
    {
    }
    public Task<CommonListPage> GetPageAsync(FilterOrganization query)
    {
        return base.GetPageAsync("sp_organization_page", query, new { query.CustomerId });
    }

    public Task<MasterCommonInfo> GetInfoAsync(System.Guid? id)
    {
        return base.GetFieldsAsync<MasterCommonInfo>("sp_organization_fields", new { id });
    }

    public Task<BaseValidate> SetInfoAsync(MasterCommonInfo info)
    {
        return base.SetInfoAsync<BaseValidate>("sp_organization_set", info, new { info.id });
    }

    public Task<BaseValidate> DeleteAsync(System.Guid? id)
    {
        return base.DeleteAsync("sp_organization_delete", new { id });
    }

}