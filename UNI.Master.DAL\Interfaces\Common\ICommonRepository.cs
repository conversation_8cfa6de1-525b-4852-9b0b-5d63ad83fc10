﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.Model;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Model.Commons;
using UNI.Model.Core;

namespace UNI.Master.DAL.Interfaces.Common
{
    public interface ICommonRepository
    {
        Task<CommonViewInfo> GetManagerFilter(string table_key);
        Task<List<CommonValue>> GetObjectList(string objKey);
        Task SetConfigData(string key, string value);
        Task<PaginationList<CommonValue>> GetSelectionListAsync(string key, string search, string refId);

        Task<List<CommonValue>> GetObjects(string objKey, string all, string filter);
        Task<List<CommonValue>> GetObjectClass(string objKey, string all);
        Task<List<CommonValue>> GetCommonList(CommonInput common);

        Task<List<CommonValue>> GetMonthList();
        Task<List<CommonValue>> GetYearList();
        Task<BaseValidate> DelMultiCommons(CommonDeleteMulti Oids);
        Task<CommonViewInfo> GetAddressDetail(Guid? Oid);
        Task<CommonViewInfo> GetAddressDetailDraft(CommonViewInfo info);

        Task<List<CommonValue>> GetCountries(string filter, string resCode);
        Task<List<CommonValue>> GetAddressStreets(AddressParam param);
        Task<List<CommonValue>> GetOrganizeList(Guid? oid, int org_level, Guid? parentId, string all, string filter);
    }
}
