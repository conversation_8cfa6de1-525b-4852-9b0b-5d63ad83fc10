﻿using Dapper;
using DapperParameters;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.DAL.Repositories.Common
{
    /// <summary>
    /// Dictionary Repository
    /// </summary>
    /// Author: 
    /// CreatedDate: 16/11/2016 2:07 PM
    /// <seealso cref="IDictionaryRepository" />
    public class DictionaryRepository : UniBaseRepository, IDictionaryRepository
    {
        /// <summary>
        /// logger
        /// </summary>

        public DictionaryRepository(
            IUniCommonBaseRepository commonInfo) : base(commonInfo)
        {
            //_logger = logger;
        }


        #region com-reg        
        public async Task<CommonViewInfo> GetDicFilter()
        {
            const string storedProcedure = "sp_hrm_dic_table_filter";
            return await base.GetFieldsAsync<CommonViewInfo>(storedProcedure, new { });
        }
        public async Task<List<DicItem>> GetDicTrees(string group_code, string filter)
        {
            const string storedProcedure = "sp_hrm_dic_table_list";
            return await base.GetListAsync<DicItem>(storedProcedure, new { group_code, filter });
        }
        public async Task<List<DicItemTree>> GetDicTreeNode(string group_code, string filter)
        {
            const string storedProcedure = "sp_hrm_dic_table_tree";
            var param = new DynamicParameters();
            param.Add("@filter", filter);
            param.Add("@group_code", group_code, DbType.String, ParameterDirection.InputOutput);
            var rs = await base.GetMultipleAsync(storedProcedure, param,
            async result =>
            {
                var data = (await result.ReadAsync<DicItemTree>()).ToList();
                data.ForEach(i => i.children = data.Where(ch => ch.parentTable == i.key).ToList());
                return data.Where(i => i.parentTable == param.Get<string>("@group_code")).ToList();
            });
            return rs;
        }
        //public List<DicItem> GetDicTreeTypes(string group_code)
        //{
        //    const string storedProcedure = "sp_hrm_dic_table_types";
        //    return GetList<DicItem>(storedProcedure, new { group_code }).ToList();
        //}
        public async Task<CommonListPage> GetDicPage(FilterBase flt, string table)
        {
            const string storedProcedure = "sp_hrm_dic_table_page";
            return await GetPageAsync(storedProcedure, flt, param =>
            {
                param.Add("@tableName", table);
                return param;
            });
        }
        public async Task<DicInfo> GetDicInfo(string tableName, Guid? Oid)
        {
            const string storedProcedure = "sp_hrm_dic_table_fields";
            return await GetFieldsAsync<DicInfo>(storedProcedure,
                param =>
                {
                    param.Add("@id", Oid);
                    param.Add("@tableName", tableName);
                    return param;
                });
        }
        public async Task<BaseValidate> SetDicInfo(DicInfo info)
        {
            const string storedProcedure = "sp_hrm_dic_table_set";
            return await GetFirstOrDefaultAsync<BaseValidate>(storedProcedure,
                param =>
                {
                    param.Add("@id", info.id);
                    param.Add("@tableName", info.tableName);
                    var flist = info.group_fields.SelectMany(f => f.fields).Select(y =>
                        new DicField { field_name = y.field_name, data_type = y.data_type, columnValue = y.columnValue ?? "" }).ToList();
                    param.AddTable("@fields", "dataFieldType", flist);
                    return param;
                });
        }
        public async Task<BaseValidate> DelDicInfo(string tableName, Guid Oid)
        {
            const string storedProcedure = "sp_hrm_common_del";
            return await base.DeleteAsync(storedProcedure, new { tableName, Oids = Oid });
        }
        public async Task<BaseValidate> DelMultiDicInfo(CommonDeleteMulti commonDeleteMulti) 
            => await base.DeleteAsync(storedProcedure: "sp_hrm_common_del",
            objParams: new
            {
                Oids = string.Join(",", commonDeleteMulti.Ids.ToArray()),
                tableName = commonDeleteMulti.TableName
            });

        #endregion -reg

        #region import & export
        public async Task<ImportListPage> SetDicImport(List<DicFieldItem> records, string tableName, bool accept)
        {
            const string storedProcedure = "sp_hrm_dic_table_import_set";
            var rs = await base.GetMultipleAsync(storedProcedure, param =>
            {
                param.Add("@accept", accept);
                param.Add("@tableName", tableName);
                param.AddTable("@fields", "DataFieldRowType", records);
                return param;
            },
            async result =>
            {
                var data = result.ReadFirst<ImportListPage>();
                data.gridflexs = (await result.ReadAsync<viewGridFlex>()).ToList();
                var list = (await result.ReadAsync<object>()).ToList();
                data.dataList = list;
                return data;
            }, commandTimeout: 9000);
            return rs;
        }
        public async Task<ImportListPage> SetDicImport(string tableName, DicImportSetV1 importSet)
        {
            const string storedProcedure = "sp_hrm_dic_table_import_set_v1";
            var rs = await base.GetMultipleAsync(storedProcedure, param =>
            {
                param.Add("@accept", importSet.accept);
                param.Add("@tableName", tableName);
                param.AddTable("@datadics", "DataDicType", importSet.imports);
                return param;
            },
            async result =>
            {
                var data = result.ReadFirst<ImportListPage>();
                data.gridflexs = (await result.ReadAsync<viewGridFlex>()).ToList();
                var list = (await result.ReadAsync<object>()).ToList();
                data.dataList = list;
                return data;
            });
            return rs;
        }
        
        public async Task<DataSet> SetDicExport(string tableName, bool includeData, string filter)
        {
            const string storedProcedure = "sp_hrm_dic_table_exports";
            var lsParameter = new Dictionary<string, Dictionary<SqlDbType, object>>
            {
                { "UserId", new Dictionary<SqlDbType, object> {{SqlDbType.NVarChar, base.CommonInfo.UserId } }},
                { "tableName", new Dictionary<SqlDbType, object> {{ SqlDbType.NVarChar, tableName } }},
                { "filter", new Dictionary<SqlDbType, object> {{ SqlDbType.NVarChar, filter } }},
                { "includeData", new Dictionary<SqlDbType, object> {{ SqlDbType.Int, includeData } }},
            };
            return await GetDataSetAsync(storedProcedure, lsParameter);
        }
        public async Task<DataSet> GetDicImportTemp()
        {
            const string storedProcedure = "sp_hrm_dic_table_import_temp";
            return await GetDataSetAsync(storedProcedure);
        }

        #endregion


    }
}

