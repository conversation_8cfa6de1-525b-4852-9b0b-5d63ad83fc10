﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.BLL.BusinessService.Common
{
    /// <summary>
    /// Interface Investment Service
    /// <author>duongpx</author>
    /// <date>2020/04/17</date>
    /// </summary>
    public class WorkflowService : IWorkflowService
    {
        private readonly IWorkflowRepository _kinvRepository;
        public WorkflowService(IWorkflowRepository siptRepository
            )
        {
            _kinvRepository = siptRepository;
        }


        #region param-reg

        public Task<List<CommonValue>> GetObjectList(string objKey)
        {
            return _kinvRepository.GetObjectList(objKey);
        }
        public Task<CommonListPage> GetWorkflowPage(FilterBase filter)
        {
            return _kinvRepository.GetWorkflowPage(filter);
        }
        public Task<userWorkFlowInfo> GetWorkflowInfo(Guid wft_id)
        {
            return _kinvRepository.GetWorkflowInfo(wft_id);
        }
        public Task<BaseValidate> SetWorkSubmit(uniWorkSubmit para)
        {
            return _kinvRepository.SetWorkSubmit(para);
        }
        public Task<BaseValidate> SetWorkApprove(uniWorkApprove para)
        {
            return _kinvRepository.SetWorkApprove(para);
        }
        public Task<CommonListPage> GetParameterPage(FilterBase flt)
        {
            return _kinvRepository.GetParameterPage(flt);
        }
        public Task<CommonViewInfo> GetInvParameter(long id)
        {
            return _kinvRepository.GetInvParameter(id);
        }
        public Task<BaseValidate> SetInvParameter(CommonViewInfo para)
        {
            return _kinvRepository.SetInvParameter(para);
        }
        #endregion param-reg
    }
}
