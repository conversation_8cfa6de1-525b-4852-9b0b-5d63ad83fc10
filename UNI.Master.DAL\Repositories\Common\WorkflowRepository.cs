﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces.Common;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.DAL.Repositories.Common
{
    /// <summary>
    /// Work Repository
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 2020-04-47
    /// <seealso cref="WorkflowRepository" />
    public class WorkflowRepository : UniBaseRepository, IWorkflowRepository
    {
        public WorkflowRepository(IUniCommonBaseRepository commonInfo) : base(commonInfo)
        {
        }

        public async Task<List<CommonValue>> GetObjectList(string objKey)
        {
            const string storedProcedure = "sp_config_object_data_get";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { objKey });
        }

        public async Task<CommonListPage> GetWorkflowPage(FilterBase filter)
        {
            const string storedProcedure = "sp_work_flow_page";
            return await GetPageAsync(storedProcedure, filter);
        }

        public async Task<userWorkFlowInfo> GetWorkflowInfo(Guid wft_id)
        {
            const string storedProcedure = "sp_work_flow_fields";
            return await base.GetFirstOrDefaultAsync<userWorkFlowInfo>(storedProcedure, new { wft_id });
        }

        public async Task<BaseValidate> SetWorkSubmit(uniWorkSubmit para)
        {
            const string storedProcedure = "sp_inv_WorkFlow_Submit";
            return await base.GetFirstOrDefaultAsync<BaseValidate>(storedProcedure, para);
        }

        public async Task<BaseValidate> SetWorkApprove(uniWorkApprove para)
        {
            const string storedProcedure = "sp_work_flow_approve";
            return await base.GetFirstOrDefaultAsync<BaseValidate>(storedProcedure, para);
        }

        public async Task<CommonListPage> GetParameterPage(FilterBase flt)
        {
            const string storedProcedure = "sp_uinv_parameter_page";
            return await GetPageAsync(storedProcedure, flt, param =>
            {
                param.Add("@mod_cd", "Kinv");
                return param;
            });
        }

        public async Task<CommonViewInfo> GetInvParameter(long id)
        {
            const string storedProcedure = "sp_uinv_parameter_fields";
            return await base.GetFieldsAsync<CommonViewInfo>(storedProcedure, new { id });
        }

        public async Task<BaseValidate> SetInvParameter(CommonViewInfo para)
        {
            const string storedProcedure = "sp_uinv_parameter_set";
            return await base.SetInfoAsync<BaseValidate>(storedProcedure, para, new { para.id });
        }

        public async Task<BaseValidate> DelInvParameter(long id)
        {
            const string storedProcedure = "sp_uinv_parameter_del";
            return await base.DeleteAsync(storedProcedure, new { id });
        }


    }
}
