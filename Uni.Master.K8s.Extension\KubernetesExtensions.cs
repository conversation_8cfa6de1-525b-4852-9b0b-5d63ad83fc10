﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using k8s;
using k8s.Models;

namespace Uni.Master.K8s.Extension
{
    public static class KubernetesExtensions
    {
        /// <summary>
        /// Apply resources
        /// </summary>
        /// <param name="client">k8s client</param>
        /// <param name="content">yaml content</param>
        /// <param name="namespace"></param>
        /// <returns></returns>
        public static async Task<Dictionary<Type, object?>> ApplyResource(this IKubernetes client, string content, string? @namespace = null)
        {
            var objects = KubernetesYaml.LoadAllFromString(content);
            var result = new Dictionary<Type, object?>();
            foreach (var obj in objects)
            {
                switch (obj)
                {
                    case V1Deployment deployment:
                        var dep = await ApplyDeployment(client, deployment, @namespace);
                        if (dep != null) result.Add(typeof(V1Deployment), dep);
                        break;
                    case V1Service service:
                        var s = await ApplyService(client, service, @namespace);
                        if (s != null) result.Add(typeof(V1Service), s);
                        break;
                    case V1ConfigMap configMap:
                        var cfg = await ApplyConfigMap(client, configMap, @namespace);
                        if (cfg != null) result.Add(typeof(V1ConfigMap), cfg);
                        break;
                    case V1Namespace ns:
                        var n = await ApplyNamespace(client, ns);
                        if (n != null) result.Add(typeof(V1Namespace), n);
                        break;
                    case V1Ingress ingress:
                        var ing = await ApplyIngress(client, ingress, @namespace);
                        if (ing != null) result.Add(typeof(V1Ingress), ing);
                        break;
                    case V1Secret secret:
                        var sec = await ApplySecret(client, secret, @namespace);
                        if (sec != null) result.Add(typeof(V1Secret), sec);
                        break;
                    default:
                        Console.WriteLine($"Unsupported resource type: {obj.GetType().Name}");
                        break;
                }
            }

            return result;
        }

        private static async Task<V1Secret> ApplySecret(IKubernetes client, V1Secret secret, string? ns)
        {
            V1Secret? rs = null;
            if (secret.StringData != null && secret.Data != null)
            {
                throw new InvalidOperationException("Cannot use both Data and StringData in the same Secret");
            }

            if (secret.Data != null)
            {
                var data = new Dictionary<string, byte[]>();
                foreach (var item in secret.Data)
                {
                    var base64Data = Encoding.UTF8.GetString(item.Value);
                    data.Add(item.Key, Convert.FromBase64String(base64Data));
                }
                secret.Data = data;
            }
            try
            {
                ns = !string.IsNullOrWhiteSpace(ns) ? ns : secret.Metadata.NamespaceProperty;
                var existing = await client.CoreV1.ReadNamespacedSecretAsync(
                    secret.Metadata.Name,
                    ns
                );


                // Patch existing secret
                rs = await client.CoreV1.ReplaceNamespacedSecretAsync(
                     body: secret,
                     name: secret.Metadata.Name,
                     namespaceParameter: ns
                 );
            }
            catch (k8s.Autorest.HttpOperationException ex) when (ex.Response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Create new deployment
                rs = await client.CoreV1.CreateNamespacedSecretAsync(
                     secret,
                     ns
                 );
            }

            return rs;
        }

        private static async Task<V1Deployment?> ApplyDeployment(IKubernetes client, V1Deployment deployment, string? ns = null)
        {
            V1Deployment? rs = null;
            try
            {
                ns = !string.IsNullOrWhiteSpace(ns) ? ns : deployment.Metadata.NamespaceProperty;
                var existing = await client.AppsV1.ReadNamespacedDeploymentAsync(
                    deployment.Metadata.Name,
                    ns
                );

                // Patch existing deployment
                rs = await client.AppsV1.ReplaceNamespacedDeploymentAsync(
                     body: deployment,
                     name: deployment.Metadata.Name,
                     namespaceParameter: ns
                 );
            }
            catch (k8s.Autorest.HttpOperationException ex) when (ex.Response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                // Create new deployment
                rs = await client.AppsV1.CreateNamespacedDeploymentAsync(
                     deployment,
                     ns
                 );
            }

            return rs;
        }

        private static async Task<V1Service?> ApplyService(IKubernetes client, V1Service service, string? ns = null)
        {
            V1Service? rs = null;
            ns = !string.IsNullOrWhiteSpace(ns) ? ns : service.Metadata.NamespaceProperty;
            try
            {

                var existing = await client.CoreV1.ReadNamespacedServiceAsync(
                    service.Metadata.Name,
                    ns
                );

                rs = await client.CoreV1.ReplaceNamespacedServiceAsync(
                     body: service,
                     name: service.Metadata.Name,
                     namespaceParameter: ns
                 );

            }
            catch (k8s.Autorest.HttpOperationException ex) when (ex.Response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                rs = await client.CoreV1.CreateNamespacedServiceAsync(
                    service,
                    ns
                );
            }
            return rs;
        }

        private static async Task<V1ConfigMap?> ApplyConfigMap(IKubernetes client, V1ConfigMap configMap, string? ns = null)
        {
            V1ConfigMap? rs = null;
            ns = !string.IsNullOrWhiteSpace(ns) ? ns : configMap.Metadata.NamespaceProperty;
            try
            {
                var existing = await client.CoreV1.ReadNamespacedConfigMapAsync(
                    configMap.Metadata.Name,
                    ns
                );

                rs = await client.CoreV1.ReplaceNamespacedConfigMapAsync(
                     body: configMap,
                     name: configMap.Metadata.Name,
                     namespaceParameter: ns
                 );
            }
            catch (k8s.Autorest.HttpOperationException ex) when (ex.Response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                rs = await client.CoreV1.CreateNamespacedConfigMapAsync(
                    configMap,
                    ns
                );
            }

            return rs;
        }

        private static async Task<V1Namespace?> ApplyNamespace(IKubernetes client, V1Namespace ns)
        {
            V1Namespace? rs = null;
            try
            {
                rs = await client.CoreV1.ReadNamespaceAsync(ns.Metadata.Name);
            }
            catch (k8s.Autorest.HttpOperationException ex) when (ex.Response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                rs = await client.CoreV1.CreateNamespaceAsync(ns);
            }

            return rs;
        }

        private static async Task<V1Ingress?> ApplyIngress(IKubernetes client, V1Ingress ingress, string? ns = null)
        {
            V1Ingress? rs = null;
            ns = !string.IsNullOrWhiteSpace(ns) ? ns : ingress.Metadata.NamespaceProperty;
            try
            {
                var existing = await client.NetworkingV1.ReadNamespacedIngressAsync(
                    ingress.Metadata.Name,
                    ns
                );

                rs = await client.NetworkingV1.ReplaceNamespacedIngressAsync(
                     body: ingress,
                     name: ingress.Metadata.Name,
                     namespaceParameter: ns
                 );
            }
            catch (k8s.Autorest.HttpOperationException ex) when (ex.Response.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                rs = await client.NetworkingV1.CreateNamespacedIngressAsync(
                    ingress,
                    ns
                );
            }

            return rs;
        }
    }
}