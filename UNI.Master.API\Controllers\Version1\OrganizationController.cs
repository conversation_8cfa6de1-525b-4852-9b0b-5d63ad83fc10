using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using UNI.Master.BLL.Interfaces;
using UNI.Master.Model.Common;
using UNI.Master.Model.Organizations;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1
{
    /// <inheritdoc />
    [Route("api/v1/[controller]/[action]")]
    [ApiController]
    public class OrganizationController : UniController
    {
        private readonly IOrganizationService _organizationService;

        /// <summary>
        /// ctor
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="organizationService"></param>
        public OrganizationController(ILoggerFactory logger, IOrganizationService organizationService) : base(logger)
        {
            _organizationService = organizationService;
        }
        /// <summary>
        /// Grid
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(IActionResult))]
        public async Task<IActionResult> GetPage([FromQuery] FilterOrganization query)
        {
            query.userId = UserId;
            CommonListPage listPage = await _organizationService.GetPageAsync(query);
            var rp = GetResponse(ApiResult.Success, listPage);
            return Ok(rp);
        }

        /// <summary>
        /// Get organization info by id
        /// </summary>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(BaseResponse<MasterCommonInfo>))]
        public async Task<IActionResult> GetInfo([FromQuery] Guid? id)
        {
            var info = await _organizationService.GetInfoAsync(id);
            var rp = GetResponse(ApiResult.Success, info);
            return Ok(rp);
        }

        /// <summary>
        /// Create or update organization info
        /// </summary>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(BaseResponse))]
        public async Task<IActionResult> SetInfo([FromBody] MasterCommonInfo info)
        {
            var result = await _organizationService.SetInfoAsync(info);
            var rp = GetResponse(result.valid ? ApiResult.Success : ApiResult.Error, result);
            return Ok(rp);
        }

        /// <summary>
        /// Delete organization by id
        /// </summary>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(BaseResponse))]
        public async Task<IActionResult> Delete([FromBody] Guid? id)
        {
            var result = await _organizationService.DeleteAsync(id);
            var rp = GetResponse(result.valid ? ApiResult.Success : ApiResult.Error, result);
            return Ok(rp);
        }

    }
}
