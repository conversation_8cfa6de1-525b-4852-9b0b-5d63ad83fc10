﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using UNI.Model;
using UNI.Model.Commons;

namespace UNI.Master.DAL.Interfaces.Common
{

    public interface IDictionaryRepository
    {
        #region dic-reg
        Task<CommonViewInfo> GetDicFilter();
        Task<CommonListPage> GetDicPage(FilterBase flt, string table);
        Task<DicInfo> GetDicInfo(string tableName, Guid? Oid);
        Task<BaseValidate> SetDicInfo(DicInfo info);
        Task<BaseValidate> DelDicInfo(string tableName, Guid Oid);
        Task<BaseValidate> DelMultiDicInfo(CommonDeleteMulti commonDeleteMulti);
        Task<List<DicItem>> GetDicTrees(string group_code, string filter);
        Task<List<DicItemTree>> GetDicTreeNode(string group_code, string filter);
        //List<DicItem> GetDicTreeTypes(string group_code);
        #endregion dic-reg

        #region import
        Task<ImportListPage> SetDicImport(List<DicFieldItem> records, string tableName, bool accept);
        //Task<ImportListPage> SetDicImportV2(BaseImportSet<DataTable> importSet, string tableName);
        //Task<ImportListPage> SetDicImportV2(DicImportSetV2 records);
        Task<ImportListPage> SetDicImport(string tableName, DicImportSetV1 info);
        Task<DataSet> SetDicExport(string tableName, bool includeData, string filter);
        Task<DataSet> GetDicImportTemp();

        #endregion
    }
}
