﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.BLL.Interfaces.Common;
using UNI.Master.Model.UniMaster;
using UNI.Model;
using UNI.Model.Api;

namespace UNI.Master.API.Controllers.Version1
{
    /// <summary>
    /// ProductPackage Controller
    /// </summary>
    /// Author: duongpx
    /// CreatedDate: 07/04/2020 9:31 AM
    /// <seealso cref="ProductPackageController" />
    [Route("api/v1/productPackage/[action]")]
    [Authorize]
    public class ProductPackageController : UniController
    {
        private readonly IProductPackageService _productPackageService;
        private readonly ICommonService _systemService;


        /// <summary>
        /// Web Controller
        /// </summary>
        /// <param name="productPackageService"></param>
        /// <param name="systemService"></param>
        /// <param name="appSettings"></param>
        /// <param name="logger"></param>
        public ProductPackageController(
            IProductPackageService productPackageService,
            ICommonService systemService,
            IOptions<AppSettings> appSettings,
            ILoggerFactory logger) : base(appSettings, logger)
        {
            _productPackageService = productPackageService;
            _systemService = systemService;
        }
        /// <summary>
        /// GetProductPackagePage - Láy ds gói sản phẩm
        /// </summary>
        /// <param name="flt"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetProductPackagePage(
            [FromQuery] FilterProdPackage flt)
        {
            try
            {
                flt.ucInput(this.UserId, this.ClientId, this.AcceptLanguage);
                //var flt = new FilterProdPackage(this.ClientId, this.UserId, offSet, pageSize, filter, gridWith, prod_id);
                var rs = await _productPackageService.GetProductPackagePage(flt);
                var rp = GetResponse(ApiResult.Success, rs);
                return rp;
            }
            catch (Exception e)
            {
                _logger.LogError($"{e}");
                var rp = new BaseResponse<CommonListPage>(ApiResult.Error, e.Message);
                return rp;
            }
        }
        /// <summary>
        /// GetProductPackageInfo  - lấy thông tin gói sp
        /// </summary>
        /// <param name="id"></param>
        /// <param name="productId"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<productPackageInfo>> GetProductPackageInfo([FromQuery] Guid? id)
        {
            var result = await _productPackageService.GetProductPackageInfo(id);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// SetProductPackageInfo - Thêm/sửa gói sản phẩm
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetProductPackageInfo([FromBody] productPackageInfo prod)
        {
            var result = await _productPackageService.SetProductPackageInfo(prod);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// DelProductPackageInfo - Xóa gói sản phẩm
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelProductPackageInfo([FromQuery] Guid id)
        {
            var result = await _productPackageService.DelProductPackageInfo(id);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// GetProductPackageList - Danh sách gói sản phẩm
        /// </summary>
        /// <param name="filter"> Tìm theo tên gói sản phẩm</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetProductPackageList([FromQuery] string filter)
        {
            var result = await _productPackageService.GetProductPackageList(filter);
            return GetResponse(ApiResult.Success, result);
        }




        #region prod-package_detail
        /// <summary>
        /// GetProductPackageDetailInfo - Lấy chi tiết gói cước sản phẩm chi tiết
        /// </summary>
        /// <param name="id"></param>
        /// <param name="productId"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<BaseResponse<ProductPackageDetailInfo>> GetProductPackageDetailInfo([FromQuery] Guid? id, [FromQuery] Guid? productId)
        {
            var result = await _productPackageService.GetProductPackageDetailInfo(id, productId);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// SetProductPackageDetailInfo - Áp dụng gói cước sản phẩm
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetProductPackageDetailInfo([FromBody] ProductPackageDetailInfo prod)
        {
            var result = await _productPackageService.SetProductPackageDetailInfo(prod);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }


        /// <summary>
        /// DelProductPackageDetailInfo - Xóa gói sản phẩm chi tiết
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelProductPackageDetailInfo([FromQuery] Guid id)
        {
            var result = await _productPackageService.DelProductPackageDetailInfo(id);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }


        /// <summary>
        /// GetProductPackageDetailList - Danh sách gói sản phẩm áp dụng
        /// </summary>
        /// <param name="filter"> Tìm theo tên gói sản phẩm</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<List<CommonValue>>> GetProductPackageDetailList([FromQuery] string filter)
        {
            var result = await _productPackageService.GetProductPackageDetailList(filter);
            return GetResponse(ApiResult.Success, result);
        }


        /// <summary>
        /// GetProductPackageDetailsPage - Láy danh sách gói sản phẩm áp dụng
        /// </summary>
        /// <param name="flt"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetProductPackageDetailPage(
            [FromQuery] FilterProdPackage flt)
        {
            try
            {
                flt.ucInput(this.UserId, this.ClientId, this.AcceptLanguage);
                //var flt = new FilterProdPackage(this.ClientId, this.UserId, offSet, pageSize, filter, gridWith, prod_id);
                var rs = await _productPackageService.GetProductPackageDetailsPage(flt);
                var rp = GetResponse(ApiResult.Success, rs);
                return rp;
            }
            catch (Exception e)
            {
                _logger.LogError($"{e}");
                var rp = new BaseResponse<CommonListPage>(ApiResult.Error, e.Message);
                return rp;
            }
        }
        #endregion prod-package-detail



        #region Product Package Detail Description
        /// <summary>
        /// GetProductPackageDetailDescriptionPage - Lấy danh sách gọi sản phẩm mô tả chi tiết
        /// </summary>
        /// <param name="flt"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetProductPackageDetailDescriptionPage(
            [FromQuery] FilterProductPackageDetailDescription flt)
        {
            try
            {
                flt.ucInput(this.UserId, this.ClientId, this.AcceptLanguage);
                //var flt = new FilterProdPackage(this.ClientId, this.UserId, offSet, pageSize, filter, gridWith, prod_id);
                var rs = await _productPackageService.GetProductPackageDetailDescriptionList(flt);
                var rp = GetResponse(ApiResult.Success, rs);
                return rp;
            }
            catch (Exception e)
            {
                _logger.LogError($"{e}");
                var rp = new BaseResponse<CommonListPage>(ApiResult.Error, e.Message);
                return rp;
            }
        }
        /// <summary>
        /// GetProductPackageDetailDescriptionInfo  - Lấy chi tiết thông tin mô tả gói sản phẩm
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<BaseResponse<ProductPackageDetailDescriptionInfo>> GetProductPackageDetailDescriptionInfo([FromQuery] ProductPackageDetailDescriptionGet data)
        {
            var result = await _productPackageService.GetProductPackageDetailDescriptionInfo(data);
            return GetResponse(ApiResult.Success, result);
        }
        /// <summary>
        /// ProductPackageDetailDescriptionInfo - Thêm/sửa chi tiết mô tả sản phẩm
        /// </summary>
        /// <param name="prod"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<string>> SetProductPackageDetailDescriptionInfo([FromBody] ProductPackageDetailDescriptionInfo prod)
        {
            var result = await _productPackageService.SetProductPackageDetailDescriptionInfo(prod);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        /// <summary>
        /// DelProductPackageDetailDescriptionInfo - Xóa chi tiết mô tả gói sản phẩm chi tiết
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<BaseResponse<string>> DelProductPackageDetailDescriptionInfo([FromQuery] Guid id)
        {
            var result = await _productPackageService.DelProductPackageDetailDescriptionInfo(id);
            if (result.valid)
                return GetResponse<string>(ApiResult.Success, result.messages);
            else
            {
                var response = GetResponse<string>(ApiResult.Error, null);
                response.SetStatus(2, result.messages);
                return response;
            }
        }
        #endregion
    }
}
