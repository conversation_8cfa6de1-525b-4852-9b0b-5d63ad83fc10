using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using UNI.Master.BLL.Interfaces.Deployments;
using UNI.Master.Model.Deployments;
using UNI.master.Test.Fixtures;
using Xunit;

namespace UNI.master.Test.IntegrationTests
{
    /// <summary>
    /// Deployment Database Service Integration Tests
    /// </summary>
    public class DeploymentDatabaseServiceTests : IClassFixture<DeploymentFixture>
    {
        private readonly DeploymentFixture _fixture;
        private readonly IDeploymentDatabaseService _deploymentDatabaseService;
        private readonly ILogger<DeploymentDatabaseServiceTests> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="fixture">Test fixture</param>
        public DeploymentDatabaseServiceTests(DeploymentFixture fixture)
        {
            _fixture = fixture;
            _deploymentDatabaseService = _fixture.ServiceProvider.GetRequiredService<IDeploymentDatabaseService>();
            _logger = _fixture.ServiceProvider.GetRequiredService<ILogger<DeploymentDatabaseServiceTests>>();
        }

        /// <summary>
        /// Test getting deployment database page
        /// </summary>
        [Fact]
        public async Task GetPageAsync_ShouldReturnValidResult()
        {
            // Arrange
            var filter = new DeploymentDatabaseFilter
            {
                offSet = 1,
                pageSize = 10
            };

            // Act
            var result = await _deploymentDatabaseService.GetPageAsync(filter);

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("GetPageAsync test completed successfully");
        }

        /// <summary>
        /// Test creating deployment database with valid request
        /// </summary>
        [Fact]
        public async Task CreateDeploymentDatabaseAsync_WithValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            var request = new CreateDeploymentDatabaseRequest
            {
                CustomerId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                DatabaseName = $"test_db_{DateTime.UtcNow:yyyyMMdd_HHmmss}",
                Notes = "Test database creation"
            };

            // Act
            var result = await _deploymentDatabaseService.CreateDeploymentDatabaseAsync(request);

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("CreateDeploymentDatabaseAsync test completed with result: {IsValid}", result.valid);
        }

        /// <summary>
        /// Test creating deployment database with invalid request
        /// </summary>
        [Fact]
        public async Task CreateDeploymentDatabaseAsync_WithInvalidRequest_ShouldReturnError()
        {
            // Arrange
            var request = new CreateDeploymentDatabaseRequest
            {
                CustomerId = Guid.Empty, // Invalid
                ProductId = Guid.NewGuid(),
                DatabaseName = "test_db"
            };

            // Act
            var result = await _deploymentDatabaseService.CreateDeploymentDatabaseAsync(request);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.valid);
            Assert.Contains("Customer ID is required", result.messages);
            _logger.LogInformation("CreateDeploymentDatabaseAsync validation test completed successfully");
        }

        /// <summary>
        /// Test getting deployment databases by customer
        /// </summary>
        [Fact]
        public async Task GetByCustomerAsync_ShouldReturnValidResult()
        {
            // Arrange
            var customerId = Guid.NewGuid();

            // Act
            var result = await _deploymentDatabaseService.GetByCustomerAsync(customerId);

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("GetByCustomerAsync test completed successfully");
        }

        /// <summary>
        /// Test provisioning database for deployment
        /// </summary>
        [Fact]
        public async Task ProvisionDatabaseAsync_WithValidParameters_ShouldReturnSuccess()
        {
            // Arrange
            var customerId = Guid.NewGuid();
            var productId = Guid.NewGuid();

            // Act
            var result = await _deploymentDatabaseService.ProvisionDatabaseAsync(customerId, productId);

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("ProvisionDatabaseAsync test completed with result: {IsValid}", result.valid);
        }

        /// <summary>
        /// Test checking database connection
        /// </summary>
        [Fact]
        public async Task CheckConnectionAsync_WithValidDatabaseId_ShouldReturnResult()
        {
            // Arrange
            var databaseId = Guid.NewGuid();

            // Act
            var result = await _deploymentDatabaseService.CheckConnectionAsync(databaseId);

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("CheckConnectionAsync test completed successfully");
        }

        /// <summary>
        /// Test updating database status
        /// </summary>
        [Fact]
        public async Task UpdateStatusAsync_WithValidParameters_ShouldReturnResult()
        {
            // Arrange
            var databaseId = Guid.NewGuid();
            var status = DeploymentDatabaseStatus.Active;

            // Act
            var result = await _deploymentDatabaseService.UpdateStatusAsync(databaseId, status);

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("UpdateStatusAsync test completed successfully");
        }

        /// <summary>
        /// Test getting database statistics
        /// </summary>
        [Fact]
        public async Task GetStatisticsAsync_ShouldReturnValidResult()
        {
            // Act
            var result = await _deploymentDatabaseService.GetStatisticsAsync();

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("GetStatisticsAsync test completed successfully");
        }

        /// <summary>
        /// Test backup database functionality
        /// </summary>
        [Fact]
        public async Task BackupDatabaseAsync_WithValidDatabaseId_ShouldReturnResult()
        {
            // Arrange
            var databaseId = Guid.NewGuid();
            var backupName = $"backup_{DateTime.UtcNow:yyyyMMdd_HHmmss}";

            // Act
            var result = await _deploymentDatabaseService.BackupDatabaseAsync(databaseId, backupName);

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("BackupDatabaseAsync test completed successfully");
        }

        /// <summary>
        /// Test restore database functionality
        /// </summary>
        [Fact]
        public async Task RestoreDatabaseAsync_WithValidParameters_ShouldReturnResult()
        {
            // Arrange
            var databaseId = Guid.NewGuid();
            var backupName = "test_backup";

            // Act
            var result = await _deploymentDatabaseService.RestoreDatabaseAsync(databaseId, backupName);

            // Assert
            Assert.NotNull(result);
            _logger.LogInformation("RestoreDatabaseAsync test completed successfully");
        }
    }
}
